import 'dart:io';
import 'package:dio/dio.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/databases/api/end_points.dart';
import 'package:gather_point/feature/host/data/models/reservation_model.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'package:gather_point/feature/home/<USER>/models/place_detail_model.dart';
import 'package:gather_point/feature/host/data/models/property_type_model.dart';
import 'package:gather_point/feature/cancellation_policies/data/models/cancellation_policy_model.dart';

class PropertiesApiService {
  final DioConsumer _dioConsumer;

  PropertiesApiService(this._dioConsumer);

  /// Get place details by ID
  Future<PlaceDetailModel> getPlaceDetails(int placeId) async {
    try {
      final response = await _dioConsumer.get('${EndPoints.itemsDetail}/$placeId');

      if (response['status'] == true) {
        return PlaceDetailModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch place details');
      }
    } catch (e) {
      throw Exception('Failed to fetch place details: ${e.toString()}');
    }
  }

  /// Get service categories for property creation
  Future<List<ServiceCategory>> getServiceCategories() async {
    try {
      final response = await _dioConsumer.get(EndPoints.serviceCategoriesList);

      if (response['success'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        return data.map((item) => ServiceCategory.fromJson(item)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch categories');
      }
    } catch (e) {
      throw Exception('Failed to fetch categories: ${e.toString()}');
    }
  }

  /// Get facilities list
  Future<List<FacilityModel>> getFacilities() async {
    try {
      final response = await _dioConsumer.get(EndPoints.facilitiesList);

      if (response['success'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        return data.map((item) => FacilityModel.fromJson(item)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch facilities');
      }
    } catch (e) {
      throw Exception('Failed to fetch facilities: ${e.toString()}');
    }
  }

  /// Get property types list
  Future<List<PropertyTypeModel>> getPropertyTypes() async {
    try {
      final response = await _dioConsumer.get(EndPoints.propertyTypesList);

      if (response['success'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        return data.map((item) => PropertyTypeModel.fromJson(item)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch property types');
      }
    } catch (e) {
      throw Exception('Failed to fetch property types: ${e.toString()}');
    }
  }

  /// Get cancellation policies list
  Future<List<CancellationPolicyModel>> getCancellationPolicies() async {
    try {
      final response = await _dioConsumer.get(EndPoints.cancellationPoliciesList);

      if (response['success'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        return data.map((item) => CancellationPolicyModel.fromJson(item)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch cancellation policies');
      }
    } catch (e) {
      throw Exception('Failed to fetch cancellation policies: ${e.toString()}');
    }
  }

  /// Create a new property
  Future<PropertyItemModel> createProperty({
    required String title,
    required String content,
    required int serviceCategoryId,
    required double price,
    double? weekendPrice,
    double? weekPrice,
    double? monthPrice,
    double? lat,
    double? lon,
    int? noGuests,
    int? beds,
    int? baths,
    String? bookingRules,
    String? cancelationRules,
    int? propertyTypeId,
    int? cancellationPolicyId,
    List<int>? facilityIds,
    File? mainImage,
    File? video,
    List<File>? galleryImages,
  }) async {
    try {
      final formData = <String, dynamic>{
        'title': title,
        'content': content,
        'service_category_id': serviceCategoryId,
        'price': price,
        if (weekendPrice != null) 'weekend_price': weekendPrice,
        if (weekPrice != null) 'week_price': weekPrice,
        if (monthPrice != null) 'month_price': monthPrice,
        if (lat != null) 'lat': lat,
        if (lon != null) 'lon': lon,
        if (noGuests != null) 'no_guests': noGuests,
        if (beds != null) 'beds': beds,
        if (baths != null) 'baths': baths,
        if (bookingRules != null) 'booking_rules': bookingRules,
        if (cancelationRules != null) 'cancelation_rules': cancelationRules,
        if (propertyTypeId != null) 'property_type_id': propertyTypeId,
        if (cancellationPolicyId != null) 'cancellation_policy_id': cancellationPolicyId,
        if (facilityIds != null) 'facility_ids': facilityIds,
      };

      // Add main image if provided
      if (mainImage != null) {
        formData['image'] = await MultipartFile.fromFile(
          mainImage.path,
          filename: mainImage.path.split('/').last,
        );
      }

      // Add video if provided
      if (video != null) {
        formData['video'] = await MultipartFile.fromFile(
          video.path,
          filename: video.path.split('/').last,
        );
      }

      // Add gallery images if provided
      if (galleryImages != null && galleryImages.isNotEmpty) {
        for (int i = 0; i < galleryImages.length; i++) {
          formData['gallery_images[$i]'] = await MultipartFile.fromFile(
            galleryImages[i].path,
            filename: galleryImages[i].path.split('/').last,
          );
        }
      }

      final response = await _dioConsumer.post(
        EndPoints.itemsCreate,
        data: formData,
        isFormData: true,
      );

      if (response['success'] == true) {
        return PropertyItemModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to create property');
      }
    } catch (e) {
      throw Exception('Failed to create property: ${e.toString()}');
    }
  }

  /// Update an existing property
  Future<PropertyItemModel> updateProperty({
    required int propertyId,
    String? title,
    String? content,
    int? serviceCategoryId,
    double? price,
    double? weekendPrice,
    double? weekPrice,
    double? monthPrice,
    double? lat,
    double? lon,
    int? noGuests,
    int? beds,
    int? baths,
    String? bookingRules,
    String? cancelationRules,
    int? propertyTypeId,
    int? cancellationPolicyId,
    List<int>? facilityIds,
    File? mainImage,
    File? video,
  }) async {
    try {
      final formData = <String, dynamic>{};

      if (title != null) formData['title'] = title;
      if (content != null) formData['content'] = content;
      if (serviceCategoryId != null) formData['service_category_id'] = serviceCategoryId;
      if (price != null) formData['price'] = price;
      if (weekendPrice != null) formData['weekend_price'] = weekendPrice;
      if (weekPrice != null) formData['week_price'] = weekPrice;
      if (monthPrice != null) formData['month_price'] = monthPrice;
      if (lat != null) formData['lat'] = lat;
      if (lon != null) formData['lon'] = lon;
      if (noGuests != null) formData['no_guests'] = noGuests;
      if (beds != null) formData['beds'] = beds;
      if (baths != null) formData['baths'] = baths;
      if (bookingRules != null) formData['booking_rules'] = bookingRules;
      if (cancelationRules != null) formData['cancelation_rules'] = cancelationRules;
      if (propertyTypeId != null) formData['property_type_id'] = propertyTypeId;
      if (cancellationPolicyId != null) formData['cancellation_policy_id'] = cancellationPolicyId;
      if (facilityIds != null) formData['facility_ids'] = facilityIds;

      // Add main image if provided
      if (mainImage != null) {
        formData['image'] = await MultipartFile.fromFile(
          mainImage.path,
          filename: mainImage.path.split('/').last,
        );
      }

      // Add video if provided
      if (video != null) {
        formData['video'] = await MultipartFile.fromFile(
          video.path,
          filename: video.path.split('/').last,
        );
      }

      final response = await _dioConsumer.put(
        '${EndPoints.itemsUpdate}/$propertyId',
        data: formData,
        isFormData: true,
      );

      if (response['success'] == true) {
        return PropertyItemModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to update property');
      }
    } catch (e) {
      throw Exception('Failed to update property: ${e.toString()}');
    }
  }

  /// Upload gallery images for a property
  Future<bool> uploadGalleryImages(int propertyId, List<File> images) async {
    try {
      final formData = <String, dynamic>{};

      for (int i = 0; i < images.length; i++) {
        formData['images[$i]'] = await MultipartFile.fromFile(
          images[i].path,
          filename: images[i].path.split('/').last,
        );
      }

      final response = await _dioConsumer.post(
        '${EndPoints.itemsUploadGallery}/$propertyId',
        data: formData,
        isFormData: true,
      );

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to upload gallery images: ${e.toString()}');
    }
  }

  /// Get user's properties
  Future<List<PropertyItemModel>> getUserProperties({
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final queryParams = {
        'page': page,
        'limit': limit,
        'user_properties': true, // To get only current user's properties
      };

      final response = await _dioConsumer.get(
        EndPoints.itemsList,
        queryParameters: queryParams,
      );

      if (response['success'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        return data.map((item) => PropertyItemModel.fromJson(item)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch properties');
      }
    } catch (e) {
      throw Exception('Failed to fetch properties: ${e.toString()}');
    }
  }

  /// Delete a property
  Future<bool> deleteProperty(int propertyId) async {
    try {
      final response = await _dioConsumer.delete('/api/items/$propertyId');
      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to delete property: ${e.toString()}');
    }
  }

  /// Toggle property active status
  Future<bool> togglePropertyStatus(int propertyId, bool active) async {
    try {
      final response = await _dioConsumer.patch(
        '/api/items/$propertyId/status',
        data: {'active': active},
      );
      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to update property status: ${e.toString()}');
    }
  }
}

class FacilityModel {
  final int id;
  final String title;
  final String? icon;
  final int order;

  const FacilityModel({
    required this.id,
    required this.title,
    this.icon,
    required this.order,
  });

  factory FacilityModel.fromJson(Map<String, dynamic> json) {
    return FacilityModel(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      icon: json['icon'],
      order: json['order'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'icon': icon,
      'order': order,
    };
  }
}
