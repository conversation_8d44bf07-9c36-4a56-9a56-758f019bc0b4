
class ServiceCategory {
  final int id;
  final String title;
  final String icon;
  final String image;
  final int order;

  const ServiceCategory({
    required this.id,
    required this.title,
    required this.icon,
    required this.image,
    required this.order,
  });

  factory ServiceCategory.fromJson(Map<String, dynamic> json) {
    return ServiceCategory(
      id: json['id'],
      title: json['title'],
      image: json['image'] ?? "",
      icon: json['icon'] ?? "",
      order: json['order'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'image': image,
      'icon': icon,
      'order': order,
    };
  }
}
