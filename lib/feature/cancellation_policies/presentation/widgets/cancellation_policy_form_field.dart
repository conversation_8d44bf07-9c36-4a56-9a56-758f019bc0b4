import 'package:flutter/material.dart';
import 'package:gather_point/core/theme/app_colors.dart';
import 'package:gather_point/core/theme/app_text_styles.dart';
import 'package:gather_point/core/extensions/context_extension.dart';
import 'package:gather_point/feature/cancellation_policies/data/models/cancellation_policy_model.dart';
import 'package:gather_point/feature/cancellation_policies/data/services/cancellation_policy_api_service.dart';
import 'package:gather_point/feature/cancellation_policies/presentation/widgets/cancellation_policy_selector.dart';
import 'package:gather_point/generated/l10n.dart';

/// A form field widget for selecting cancellation policies
/// Use this in property creation/editing forms
class CancellationPolicyFormField extends StatefulWidget {
  final CancellationPolicyModel? initialValue;
  final Function(CancellationPolicyModel?) onChanged;
  final CancellationPolicyApiService apiService;
  final String? errorText;
  final bool isRequired;

  const CancellationPolicyFormField({
    super.key,
    this.initialValue,
    required this.onChanged,
    required this.apiService,
    this.errorText,
    this.isRequired = false,
  });

  @override
  State<CancellationPolicyFormField> createState() => _CancellationPolicyFormFieldState();
}

class _CancellationPolicyFormFieldState extends State<CancellationPolicyFormField> {
  List<CancellationPolicyModel> _policies = [];
  bool _isLoading = false;
  String? _error;
  CancellationPolicyModel? _selectedPolicy;

  @override
  void initState() {
    super.initState();
    _selectedPolicy = widget.initialValue;
    _loadPolicies();
  }

  Future<void> _loadPolicies() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final policies = await widget.apiService.getCancellationPolicies();
      setState(() {
        _policies = policies;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _onPolicySelected(CancellationPolicyModel? policy) {
    setState(() {
      _selectedPolicy = policy;
    });
    widget.onChanged(policy);
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Field label
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          child: Row(
            children: [
              Text(
                s.selectCancellationPolicy,
                style: AppTextStyles.font16SemiBold.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
              if (widget.isRequired) ...[
                const SizedBox(width: 4),
                Text(
                  '*',
                  style: AppTextStyles.font16SemiBold.copyWith(
                    color: Colors.red,
                  ),
                ),
              ],
            ],
          ),
        ),

        // Error state
        if (_error != null)
          Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.error_outline, color: Colors.red.shade700, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _error!,
                    style: AppTextStyles.font14Regular.copyWith(
                      color: Colors.red.shade700,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: _loadPolicies,
                  icon: Icon(Icons.refresh, color: Colors.red.shade700),
                ),
              ],
            ),
          ),

        // Policy selector
        if (_error == null)
          CancellationPolicySelector(
            policies: _policies,
            selectedPolicy: _selectedPolicy,
            onPolicySelected: _onPolicySelected,
            isLoading: _isLoading,
          ),

        // Validation error
        if (widget.errorText != null)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            child: Text(
              widget.errorText!,
              style: AppTextStyles.font12Regular.copyWith(
                color: Colors.red,
              ),
            ),
          ),

        // Selected policy summary
        if (_selectedPolicy != null && _error == null)
          Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.yellow.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.yellow.withValues(alpha: 0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.check_circle, color: AppColors.yellow, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'السياسة المحددة',
                      style: AppTextStyles.font14SemiBold.copyWith(
                        color: AppColors.yellow,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  _selectedPolicy!.name,
                  style: AppTextStyles.font16SemiBold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _selectedPolicy!.formattedDescription,
                  style: AppTextStyles.font14Regular.copyWith(
                    color: context.secondaryTextColor,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}

/// Example usage in a property form:
/// 
/// ```dart
/// CancellationPolicyFormField(
///   initialValue: property.cancellationPolicy,
///   onChanged: (policy) {
///     setState(() {
///       selectedCancellationPolicy = policy;
///     });
///   },
///   apiService: getIt<CancellationPolicyApiService>(),
///   isRequired: true,
///   errorText: _validateCancellationPolicy(),
/// )
/// ```
