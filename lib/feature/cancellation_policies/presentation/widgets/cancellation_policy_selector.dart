import 'package:flutter/material.dart';
import 'package:gather_point/core/theme/app_colors.dart';
import 'package:gather_point/core/theme/app_text_styles.dart';
import 'package:gather_point/core/extensions/context_extension.dart';
import 'package:gather_point/feature/cancellation_policies/data/models/cancellation_policy_model.dart';
import 'package:gather_point/generated/l10n.dart';

class CancellationPolicySelector extends StatefulWidget {
  final List<CancellationPolicyModel> policies;
  final CancellationPolicyModel? selectedPolicy;
  final Function(CancellationPolicyModel?) onPolicySelected;
  final bool isLoading;

  const CancellationPolicySelector({
    super.key,
    required this.policies,
    this.selectedPolicy,
    required this.onPolicySelected,
    this.isLoading = false,
  });

  @override
  State<CancellationPolicySelector> createState() => _CancellationPolicySelectorState();
}

class _CancellationPolicySelectorState extends State<CancellationPolicySelector> {
  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    if (widget.isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.yellow,
        ),
      );
    }

    if (widget.policies.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(20),
        child: Text(
          'No cancellation policies available',
          style: AppTextStyles.font14Regular.copyWith(
            color: context.secondaryTextColor,
          ),
        ),
      );
    }

    // Group policies by duration type
    final shortTermPolicies = widget.policies
        .where((policy) => policy.durationType == 'short' || policy.durationType == 'both')
        .toList();
    final longTermPolicies = widget.policies
        .where((policy) => policy.durationType == 'long' || policy.durationType == 'both')
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Padding(
          padding: const EdgeInsets.all(20),
          child: Text(
            s.selectCancellationPolicy,
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
        ),

        // Short-term policies
        if (shortTermPolicies.isNotEmpty) ...[
          _buildSectionHeader(s.shortTermBookings),
          ...shortTermPolicies.map((policy) => _buildPolicyTile(policy)),
          const SizedBox(height: 20),
        ],

        // Long-term policies
        if (longTermPolicies.isNotEmpty) ...[
          _buildSectionHeader(s.longTermBookings),
          ...longTermPolicies.map((policy) => _buildPolicyTile(policy)),
        ],
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Text(
        title,
        style: AppTextStyles.font16SemiBold.copyWith(
          color: context.primaryTextColor,
        ),
      ),
    );
  }

  Widget _buildPolicyTile(CancellationPolicyModel policy) {
    final isSelected = widget.selectedPolicy?.id == policy.id;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
      decoration: BoxDecoration(
        color: context.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected 
              ? AppColors.yellow 
              : context.secondaryTextColor.withValues(alpha: 0.1),
          width: isSelected ? 2 : 1,
        ),
        boxShadow: isSelected ? [
          BoxShadow(
            color: AppColors.yellow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ] : null,
      ),
      child: RadioListTile<CancellationPolicyModel>(
        value: policy,
        groupValue: widget.selectedPolicy,
        onChanged: widget.onPolicySelected,
        activeColor: AppColors.yellow,
        title: Text(
          policy.name,
          style: AppTextStyles.font16SemiBold.copyWith(
            color: context.primaryTextColor,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              policy.formattedDescription,
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor,
                height: 1.4,
              ),
            ),
            const SizedBox(height: 8),
            
            // Policy details
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.yellow.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${policy.refundPercentage.toInt()}% استرداد',
                    style: AppTextStyles.font12SemiBold.copyWith(
                      color: AppColors.yellow,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                if (policy.cancellationWindowHours > 0)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      policy.formattedCancellationWindow,
                      style: AppTextStyles.font12Regular.copyWith(
                        color: Colors.blue.shade700,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 4),
          ],
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
    );
  }
}
