import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';
import 'package:gather_point/feature/host/presentation/cubit/property_creation_cubit.dart';

class EditPropertyForm extends StatefulWidget {
  final MyListingModel propertyData;

  const EditPropertyForm({
    super.key,
    required this.propertyData,
  });

  @override
  State<EditPropertyForm> createState() => _EditPropertyFormState();
}

class _EditPropertyFormState extends State<EditPropertyForm> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  final _priceController = TextEditingController();
  final _addressController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    _titleController.text = widget.propertyData.title;
    _contentController.text = widget.propertyData.content;
    _priceController.text = widget.propertyData.price.toString();
    _addressController.text = widget.propertyData.address ?? '';
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _priceController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return BlocListener<PropertyCreationCubit, PropertyCreationState>(
      listener: (context, state) {
        if (state is PropertyUpdateSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(s.propertyUpdatedSuccessfully),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        } else if (state is PropertyCreationError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Property Images Section
              _buildImagesSection(s),

              const SizedBox(height: 24),

              // Basic Information
              _buildBasicInfoSection(s),

              const SizedBox(height: 24),

              // Location Section
              _buildLocationSection(s),

              const SizedBox(height: 24),

              // Pricing Section
              _buildPricingSection(s),

              const SizedBox(height: 32),

              // Action Buttons
              _buildActionButtons(s),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImagesSection(S s) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          s.propertyImages,
          style: AppTextStyles.font16SemiBold,
        ),
        const SizedBox(height: 12),
        Container(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: widget.propertyData.galleryImages.length + 1,
            itemBuilder: (context, index) {
              if (index == widget.propertyData.galleryImages.length) {
                return _buildAddImageButton();
              }
              return _buildImageItem(widget.propertyData.galleryImages[index]);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildImageItem(String imageUrl) {
    return Container(
      width: 120,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        image: DecorationImage(
          image: NetworkImage(imageUrl),
          fit: BoxFit.cover,
        ),
      ),
      child: Stack(
        children: [
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap: () {
                // Handle image removal
              },
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddImageButton() {
    return GestureDetector(
      onTap: () {
        // Handle add image
      },
      child: Container(
        width: 120,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add_photo_alternate, size: 32, color: Colors.grey),
            SizedBox(height: 8),
            Text('إضافة صورة', style: TextStyle(color: Colors.grey)),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection(S s) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          s.basicInformation,
          style: AppTextStyles.font16SemiBold,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _titleController,
          decoration: InputDecoration(
            labelText: s.propertyTitle,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return s.pleaseEnterTitle;
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _contentController,
          maxLines: 4,
          decoration: InputDecoration(
            labelText: s.propertyDescription,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return s.pleaseEnterDescription;
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildLocationSection(S s) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          s.location,
          style: AppTextStyles.font16SemiBold,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _addressController,
          decoration: InputDecoration(
            labelText: s.address,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            suffixIcon: IconButton(
              onPressed: () {
                // Handle map picker
              },
              icon: const Icon(Icons.location_on),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPricingSection(S s) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          s.pricing,
          style: AppTextStyles.font16SemiBold,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _priceController,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            labelText: s.pricePerNight,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            suffixText: 'ر.س',
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return s.pleaseEnterPrice;
            }
            if (double.tryParse(value) == null) {
              return s.pleaseEnterValidPrice;
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildActionButtons(S s) {
    return BlocBuilder<PropertyCreationCubit, PropertyCreationState>(
      builder: (context, state) {
        final isLoading = state is PropertyCreationLoading;

        return Column(
          children: [
            // Save as Draft
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: isLoading ? null : _saveAsDraft,
                icon: const Icon(Icons.save),
                label: Text(s.saveAsDraft),
              ),
            ),
            const SizedBox(height: 12),
            // Update Property
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: isLoading ? null : _updateProperty,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFEC53A),
                  foregroundColor: Colors.black,
                ),
                icon: isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.update),
                label: Text(s.updateProperty),
              ),
            ),
          ],
        );
      },
    );
  }

  void _saveAsDraft() {
    if (_formKey.currentState!.validate()) {
      final propertyData = _getFormData();
      context.read<PropertyCreationCubit>().savePropertyAsDraft(
        widget.propertyData.id,
        propertyData,
      );
    }
  }

  void _updateProperty() {
    if (_formKey.currentState!.validate()) {
      final propertyData = _getFormData();
      context.read<PropertyCreationCubit>().updateProperty(
        propertyId: widget.propertyData.id,
        propertyData: propertyData,
      );
    }
  }

  Map<String, dynamic> _getFormData() {
    return {
      'title': _titleController.text,
      'content': _contentController.text,
      'price': double.tryParse(_priceController.text) ?? 0.0,
      'address': _addressController.text,
      'lat': widget.propertyData.lat,
      'lon': widget.propertyData.lon,
      'service_category_id': widget.propertyData.category.id,
      'no_guests': widget.propertyData.noGuests,
      'beds': widget.propertyData.beds,
      'baths': widget.propertyData.baths,
    };
  }
}
