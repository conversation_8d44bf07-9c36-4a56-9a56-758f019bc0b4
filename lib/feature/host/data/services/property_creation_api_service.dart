import 'dart:io';
import 'package:dio/dio.dart';
import 'package:gather_point/core/databases/api/api_consumer.dart';
import 'package:gather_point/core/databases/api/end_points.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';

/// Production-ready API service for property creation with validation and file upload
class PropertyCreationApiService {
  final ApiConsumer _apiConsumer;

  PropertyCreationApiService(this._apiConsumer);

  /// Create a new property with full validation and file uploads
  Future<MyListingModel> createProperty({
    required Map<String, dynamic> propertyData,
    List<File>? images,
    File? video,
  }) async {
    try {
      // Step 1: Validate property data
      _validatePropertyData(propertyData);

      // Step 2: Upload images if provided
      List<String> imageUrls = [];
      if (images != null && images.isNotEmpty) {
        imageUrls = await _uploadImages(images);
        propertyData['images'] = imageUrls;
        propertyData['main_image'] = imageUrls.first;
      }

      // Step 3: Upload video if provided
      if (video != null) {
        final videoUrl = await _uploadVideo(video);
        propertyData['video_url'] = videoUrl;
      }

      // Step 4: Create property via API
      final response = await _apiConsumer.post(
        EndPoints.createProperty,
        data: propertyData,
      );

      // Step 5: Parse and return the created property
      return MyListingModel.fromJson(response['data']);
    } catch (e) {
      throw _handleApiError(e);
    }
  }

  /// Upload multiple images for property
  Future<List<String>> _uploadImages(List<File> images) async {
    final List<String> imageUrls = [];

    for (int i = 0; i < images.length; i++) {
      try {
        final imageUrl = await _uploadSingleImage(images[i], i);
        imageUrls.add(imageUrl);
      } catch (e) {
        throw Exception('Failed to upload image ${i + 1}: $e');
      }
    }

    return imageUrls;
  }

  /// Upload a single image file
  Future<String> _uploadSingleImage(File image, int index) async {
    try {
      final formData = FormData.fromMap({
        'image': await MultipartFile.fromFile(
          image.path,
          filename: 'property_image_$index.jpg',
        ),
        'type': 'property',
        'index': index,
      });

      final response = await _apiConsumer.post(
        EndPoints.uploadImage,
        data: formData,
      );

      return response['data']['url'];
    } catch (e) {
      throw Exception('Failed to upload image: $e');
    }
  }

  /// Upload video file
  Future<String> _uploadVideo(File video) async {
    try {
      final formData = FormData.fromMap({
        'video': await MultipartFile.fromFile(
          video.path,
          filename: 'property_video.mp4',
        ),
        'type': 'property',
      });

      final response = await _apiConsumer.post(
        EndPoints.uploadVideo,
        data: formData,
      );

      return response['data']['url'];
    } catch (e) {
      throw Exception('Failed to upload video: $e');
    }
  }

  /// Save property draft (for auto-save functionality)
  Future<Map<String, dynamic>> saveDraft({
    required Map<String, dynamic> draftData,
    String? draftId,
  }) async {
    try {
      final endpoint = draftId != null 
          ? '${EndPoints.propertyDrafts}/$draftId'
          : EndPoints.propertyDrafts;

      final response = await _apiConsumer.post(
        endpoint,
        data: {
          'draft_data': draftData,
          'step': draftData['current_step'] ?? 0,
          'last_updated': DateTime.now().toIso8601String(),
        },
      );

      return response['data'];
    } catch (e) {
      throw Exception('Failed to save draft: $e');
    }
  }

  /// Load property draft
  Future<Map<String, dynamic>?> loadDraft(String draftId) async {
    try {
      final response = await _apiConsumer.get(
        '${EndPoints.propertyDrafts}/$draftId',
      );

      return response['data'];
    } catch (e) {
      // Return null if draft not found
      return null;
    }
  }

  /// Get property creation metadata (categories, types, policies, facilities)
  Future<Map<String, dynamic>> getCreationMetadata() async {
    try {
      final response = await _apiConsumer.get(
        EndPoints.propertyMetadata,
      );

      return response['data'];
    } catch (e) {
      throw Exception('Failed to load property metadata: $e');
    }
  }

  /// Validate property data before submission
  void _validatePropertyData(Map<String, dynamic> data) {
    final errors = <String>[];

    // Basic information validation
    if (data['title'] == null || data['title'].toString().trim().isEmpty) {
      errors.add('Property title is required');
    } else if (data['title'].toString().trim().length < 3) {
      errors.add('Property title must be at least 3 characters');
    }

    if (data['content'] == null || data['content'].toString().trim().isEmpty) {
      errors.add('Property description is required');
    } else if (data['content'].toString().trim().length < 10) {
      errors.add('Property description must be at least 10 characters');
    }

    if (data['price'] == null || data['price'] <= 0) {
      errors.add('Valid price is required');
    } else if (data['price'] < 50) {
      errors.add('Minimum price is 50 SAR per night');
    }

    // Property details validation
    if (data['no_guests'] == null || data['no_guests'] <= 0 || data['no_guests'] > 20) {
      errors.add('Number of guests must be between 1 and 20');
    }

    if (data['beds'] == null || data['beds'] <= 0 || data['beds'] > 10) {
      errors.add('Number of bedrooms must be between 1 and 10');
    }

    if (data['baths'] == null || data['baths'] <= 0 || data['baths'] > 10) {
      errors.add('Number of bathrooms must be between 1 and 10');
    }

    // Category and type validation
    if (data['service_category_id'] == null) {
      errors.add('Property category is required');
    }

    if (data['property_type_id'] == null) {
      errors.add('Property type is required');
    }

    if (data['cancellation_policy_id'] == null) {
      errors.add('Cancellation policy is required');
    }

    // Location validation
    if (data['lat'] == null || data['lon'] == null) {
      errors.add('Property location is required');
    }

    // Facilities validation
    if (data['facility_ids'] == null || (data['facility_ids'] as List).isEmpty) {
      errors.add('At least one facility must be selected');
    }

    if (errors.isNotEmpty) {
      throw ValidationException(errors);
    }
  }

  /// Handle API errors and convert to user-friendly messages
  Exception _handleApiError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return Exception('Connection timeout. Please check your internet connection.');
        
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          final message = error.response?.data?['message'] ?? 'Server error occurred';
          
          switch (statusCode) {
            case 400:
              return ValidationException([message]);
            case 401:
              return Exception('Authentication required. Please login again.');
            case 403:
              return Exception('You do not have permission to create properties.');
            case 422:
              final errors = error.response?.data?['errors'] as Map<String, dynamic>?;
              if (errors != null) {
                final errorMessages = errors.values
                    .expand((e) => e is List ? e : [e])
                    .map((e) => e.toString())
                    .toList();
                return ValidationException(errorMessages);
              }
              return ValidationException([message]);
            case 500:
              return Exception('Server error. Please try again later.');
            default:
              return Exception(message);
          }
        
        case DioExceptionType.cancel:
          return Exception('Request was cancelled');
        
        case DioExceptionType.unknown:
          return Exception('Network error. Please check your connection.');
        
        default:
          return Exception('An unexpected error occurred');
      }
    }
    
    if (error is ValidationException) {
      return error;
    }
    
    return Exception('An unexpected error occurred: $error');
  }
}

/// Custom exception for validation errors
class ValidationException implements Exception {
  final List<String> errors;

  ValidationException(this.errors);

  @override
  String toString() {
    return errors.join('\n');
  }
}


