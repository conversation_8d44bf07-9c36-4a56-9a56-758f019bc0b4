import 'package:dio/dio.dart';
import 'package:gather_point/core/databases/api/api_consumer.dart';
import 'package:gather_point/feature/host/data/services/my_listings_api_service.dart';
import 'package:gather_point/feature/host/data/services/mock_my_listings_api_service.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';
import 'package:gather_point/feature/host/data/models/listing_stats_model.dart';

/// Hybrid service that tries real API first, falls back to mock data on failure
/// This provides a seamless experience during development and production
class HybridMyListingsApiService extends MyListingsApiService {
  final MyListingsApiService _realApiService;
  final MockMyListingsApiService _mockApiService;
  bool _useRealApi = true;

  HybridMyListingsApiService(ApiConsumer apiConsumer)
      : _realApiService = MyListingsApiService(apiConsumer),
        _mockApiService = MockMyListingsApiService(),
        super(apiConsumer);

  /// Try real API first, fallback to mock on failure
  Future<T> _tryRealApiFallbackToMock<T>(
    Future<T> Function() realApiCall,
    Future<T> Function() mockApiCall,
  ) async {
    if (!_useRealApi) {
      return await mockApiCall();
    }

    try {
      return await realApiCall();
    } catch (e) {
      // Check if it's a 404 or network error
      if (e is DioException) {
        final statusCode = e.response?.statusCode;
        if (statusCode == 404 || statusCode == 500 || e.type == DioExceptionType.connectionTimeout) {
          // Backend not ready, switch to mock mode
          _useRealApi = false;
          print('🔄 Backend not available (${statusCode ?? e.type}), switching to mock data');
          return await mockApiCall();
        }
      }
      
      // For other errors, still try mock as fallback
      print('⚠️ API error: $e, falling back to mock data');
      return await mockApiCall();
    }
  }

  @override
  Future<List<MyListingModel>> getMyListings({
    int page = 1,
    int limit = 20,
    String? status,
    String? category,
    String? sortBy,
    String? sortOrder = 'desc',
    String? search,
  }) async {
    return await _tryRealApiFallbackToMock(
      () => _realApiService.getMyListings(
        page: page,
        limit: limit,
        status: status,
        category: category,
        sortBy: sortBy,
        sortOrder: sortOrder,
        search: search,
      ),
      () => _mockApiService.getMyListings(
        page: page,
        limit: limit,
        status: status,
        category: category,
        sortBy: sortBy,
        sortOrder: sortOrder,
        search: search,
      ),
    );
  }

  @override
  Future<ListingStatsModel> getListingStats() async {
    return await _tryRealApiFallbackToMock(
      () => _realApiService.getListingStats(),
      () => _mockApiService.getListingStats(),
    );
  }

  @override
  Future<bool> toggleListingStatus(int listingId) async {
    return await _tryRealApiFallbackToMock(
      () => _realApiService.toggleListingStatus(listingId),
      () => _mockApiService.toggleListingStatus(listingId),
    );
  }

  @override
  Future<bool> deleteListing(int listingId) async {
    return await _tryRealApiFallbackToMock(
      () => _realApiService.deleteListing(listingId),
      () => _mockApiService.deleteListing(listingId),
    );
  }

  @override
  Future<MyListingModel> duplicateListing(int listingId) async {
    return await _tryRealApiFallbackToMock(
      () => _realApiService.duplicateListing(listingId),
      () => _mockApiService.duplicateListing(listingId),
    );
  }

  @override
  Future<bool> bulkUpdateStatus(List<int> listingIds, String status) async {
    return await _tryRealApiFallbackToMock(
      () => _realApiService.bulkUpdateStatus(listingIds, status),
      () => _mockApiService.bulkUpdateStatus(listingIds, status),
    );
  }

  @override
  Future<bool> bulkDeleteListings(List<int> listingIds) async {
    return await _tryRealApiFallbackToMock(
      () => _realApiService.bulkDeleteListings(listingIds),
      () => _mockApiService.bulkDeleteListings(listingIds),
    );
  }

  @override
  Future<Map<String, dynamic>> getListingAnalytics(
    int listingId, {
    String period = '30d',
  }) async {
    return await _tryRealApiFallbackToMock(
      () => _realApiService.getListingAnalytics(listingId, period: period),
      () => _mockApiService.getListingAnalytics(listingId, period: period),
    );
  }

  @override
  Future<bool> updateListingQuickSettings(
    int listingId, {
    double? price,
    double? weekendPrice,
    double? weeklyPrice,
    double? monthlyPrice,
    bool? isAvailable,
    int? minStay,
    int? maxStay,
  }) async {
    return await _tryRealApiFallbackToMock(
      () => _realApiService.updateListingQuickSettings(
        listingId,
        price: price,
        weekendPrice: weekendPrice,
        weeklyPrice: weeklyPrice,
        monthlyPrice: monthlyPrice,
        isAvailable: isAvailable,
        minStay: minStay,
        maxStay: maxStay,
      ),
      () => _mockApiService.updateListingQuickSettings(
        listingId,
        price: price,
        weekendPrice: weekendPrice,
        weeklyPrice: weeklyPrice,
        monthlyPrice: monthlyPrice,
        isAvailable: isAvailable,
        minStay: minStay,
        maxStay: maxStay,
      ),
    );
  }

  @override
  Future<Map<String, dynamic>> getListingCalendar(
    int listingId, {
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    return await _tryRealApiFallbackToMock(
      () => _realApiService.getListingCalendar(
        listingId,
        startDate: startDate,
        endDate: endDate,
      ),
      () => _mockApiService.getListingCalendar(
        listingId,
        startDate: startDate,
        endDate: endDate,
      ),
    );
  }

  @override
  Future<bool> updateListingAvailability(
    int listingId, {
    required List<String> dates,
    required bool isAvailable,
    double? customPrice,
  }) async {
    return await _tryRealApiFallbackToMock(
      () => _realApiService.updateListingAvailability(
        listingId,
        dates: dates,
        isAvailable: isAvailable,
        customPrice: customPrice,
      ),
      () => _mockApiService.updateListingAvailability(
        listingId,
        dates: dates,
        isAvailable: isAvailable,
        customPrice: customPrice,
      ),
    );
  }

  @override
  Future<Map<String, dynamic>> getListingReviews(
    int listingId, {
    int page = 1,
    int limit = 10,
  }) async {
    return await _tryRealApiFallbackToMock(
      () => _realApiService.getListingReviews(listingId, page: page, limit: limit),
      () => _mockApiService.getListingReviews(listingId, page: page, limit: limit),
    );
  }

  @override
  Future<List<dynamic>> getListingReservations(
    int listingId, {
    String? status,
    int page = 1,
    int limit = 20,
  }) async {
    return await _tryRealApiFallbackToMock(
      () => _realApiService.getListingReservations(
        listingId,
        status: status,
        page: page,
        limit: limit,
      ),
      () => _mockApiService.getListingReservations(
        listingId,
        status: status,
        page: page,
        limit: limit,
      ),
    );
  }

  /// Check if currently using real API or mock
  bool get isUsingRealApi => _useRealApi;

  /// Force switch to real API (for testing)
  void forceUseRealApi() {
    _useRealApi = true;
  }

  /// Force switch to mock API (for testing)
  void forceUseMockApi() {
    _useRealApi = false;
  }
}
