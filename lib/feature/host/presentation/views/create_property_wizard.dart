import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:latlong2/latlong.dart';
import 'package:video_player/video_player.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';

import 'package:gather_point/feature/host/presentation/cubit/property_creation_cubit.dart';

import 'package:gather_point/feature/host/data/services/property_creation_api_service.dart';
import 'package:gather_point/feature/dashboard/data/services/properties_api_service.dart';
import 'package:gather_point/feature/host/services/property_edit_service.dart';
import 'package:gather_point/feature/host/data/models/property_metadata_models.dart';
import 'package:gather_point/core/services/service_locator.dart';

/// Simple wizard-style property creation form
class CreatePropertyWizard extends StatelessWidget {
  const CreatePropertyWizard({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        try {
          return PropertyCreationCubit(
            getIt<PropertiesApiService>(),
            getIt.isRegistered<PropertyEditService>() ? getIt<PropertyEditService>() : null,
          )..loadInitialData();
        } catch (e) {
          debugPrint('Error creating PropertyCreationCubit: $e');
          return PropertyCreationCubit(getIt<PropertiesApiService>());
        }
      },
      child: const _CreatePropertyWizardContent(),
    );
  }
}

class _CreatePropertyWizardContent extends StatefulWidget {
  const _CreatePropertyWizardContent();

  @override
  State<_CreatePropertyWizardContent> createState() => _CreatePropertyWizardContentState();
}

class _CreatePropertyWizardContentState extends State<_CreatePropertyWizardContent> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  final int _totalSteps = 6; // Increased to include gallery and location

  // Form controllers
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _guestsController = TextEditingController();
  final _bedsController = TextEditingController();
  final _bathsController = TextEditingController();

  // Form data
  String? _selectedCategory;
  String? _selectedPropertyType;
  String? _selectedCancellationPolicy;
  final List<int> _selectedFacilities = [];

  // Media and location
  final List<File> _imageGallery = [];
  File? _videoFile;
  LatLng? _selectedLocation;
  String? _locationAddress;
  final ImagePicker _picker = ImagePicker();
  VideoPlayerController? _videoController;

  // Auto-save and server sync
  Timer? _autoSaveTimer;
  bool _isAutoSaving = false;
  DateTime? _lastSyncTime;

  @override
  void initState() {
    super.initState();
    _setupAutoSave();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _guestsController.dispose();
    _bedsController.dispose();
    _bathsController.dispose();
    _autoSaveTimer?.cancel();
    _videoController?.dispose();
    super.dispose();
  }

  void _setupAutoSave() {
    _autoSaveTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _performAutoSave();
    });
  }

  Future<void> _performAutoSave() async {
    if (_isAutoSaving) return;

    setState(() => _isAutoSaving = true);

    try {
      // Save current step data to server
      await _saveStepToServer(_currentStep);
      _lastSyncTime = DateTime.now();

      if (mounted) {
        final timeStr = _lastSyncTime != null
            ? '${_lastSyncTime!.hour.toString().padLeft(2, '0')}:${_lastSyncTime!.minute.toString().padLeft(2, '0')}'
            : '';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.cloud_done, color: Colors.white, size: 16),
                const SizedBox(width: 8),
                Text('Auto-saved $timeStr', style: AppTextStyles.font12Medium.copyWith(color: Colors.white)),
              ],
            ),
            backgroundColor: Colors.green.withValues(alpha: 0.8),
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
            margin: const EdgeInsets.only(bottom: 100, left: 16, right: 16),
          ),
        );
      }
    } catch (e) {
      debugPrint('Auto-save failed: $e');
    } finally {
      setState(() => _isAutoSaving = false);
    }
  }

  Future<void> _saveStepToServer(int step) async {
    // Implementation for saving step data to server
    final stepData = _getStepData(step);
    if (stepData.isNotEmpty) {
      // Here you would call your API to save the step data
      await Future.delayed(const Duration(milliseconds: 500)); // Simulate API call
    }
  }

  Map<String, dynamic> _getStepData(int step) {
    switch (step) {
      case 0:
        return {
          'title': _titleController.text,
          'description': _descriptionController.text,
          'price': _priceController.text,
        };
      case 1:
        return {
          'category': _selectedCategory,
          'property_type': _selectedPropertyType,
          'cancellation_policy': _selectedCancellationPolicy,
        };
      case 2:
        return {
          'guests': _guestsController.text,
          'beds': _bedsController.text,
          'baths': _bathsController.text,
          'facilities': _selectedFacilities,
        };
      case 3:
        return {
          'location': _selectedLocation != null ? {
            'lat': _selectedLocation!.latitude,
            'lng': _selectedLocation!.longitude,
            'address': _locationAddress,
          } : null,
        };
      case 4:
        return {
          'images_count': _imageGallery.length,
          'has_video': _videoFile != null,
        };
      default:
        return {};
    }
  }

  void _nextStep() {
    if (_validateCurrentStep()) {
      if (_currentStep < _totalSteps - 1) {
        setState(() => _currentStep++);
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        // Save current step to server
        _saveStepToServer(_currentStep - 1);
      }
    } else {
      _showValidationErrors();
    }
  }

  bool _validateCurrentStep() {
    final s = S.of(context);

    switch (_currentStep) {
      case 0: // Basic Information
        if (_titleController.text.trim().isEmpty) {
          _showErrorSnackBar(s.propertyTitleRequired);
          return false;
        }
        if (_titleController.text.trim().length < 3) {
          _showErrorSnackBar(s.propertyTitleTooShort);
          return false;
        }
        if (_descriptionController.text.trim().isEmpty) {
          _showErrorSnackBar(s.propertyDescriptionRequired);
          return false;
        }
        if (_descriptionController.text.trim().length < 10) {
          _showErrorSnackBar(s.propertyDescriptionTooShort);
          return false;
        }
        if (_priceController.text.trim().isEmpty) {
          _showErrorSnackBar(s.priceRequired);
          return false;
        }
        final price = double.tryParse(_priceController.text);
        if (price == null || price <= 0) {
          _showErrorSnackBar(s.priceInvalid);
          return false;
        }
        if (price < 50) {
          _showErrorSnackBar(s.priceMinimum);
          return false;
        }
        break;

      case 1: // Category & Type
        if (_selectedCategory == null) {
          _showErrorSnackBar(s.categoryRequired);
          return false;
        }
        if (_selectedPropertyType == null) {
          _showErrorSnackBar(s.propertyTypeRequired);
          return false;
        }
        if (_selectedCancellationPolicy == null) {
          _showErrorSnackBar(s.cancellationPolicyRequired);
          return false;
        }
        break;

      case 2: // Property Details
        if (_guestsController.text.trim().isEmpty) {
          _showErrorSnackBar(s.guestsRequired);
          return false;
        }
        final guests = int.tryParse(_guestsController.text);
        if (guests == null || guests <= 0 || guests > 20) {
          _showErrorSnackBar(s.guestsInvalid);
          return false;
        }
        if (_bedsController.text.trim().isEmpty) {
          _showErrorSnackBar(s.bedsRequired);
          return false;
        }
        final beds = int.tryParse(_bedsController.text);
        if (beds == null || beds <= 0 || beds > 10) {
          _showErrorSnackBar(s.bedsInvalid);
          return false;
        }
        if (_bathsController.text.trim().isEmpty) {
          _showErrorSnackBar(s.bathsRequired);
          return false;
        }
        final baths = int.tryParse(_bathsController.text);
        if (baths == null || baths <= 0 || baths > 10) {
          _showErrorSnackBar(s.bathsInvalid);
          return false;
        }
        if (_selectedFacilities.isEmpty) {
          _showErrorSnackBar(s.facilitiesRequired);
          return false;
        }
        break;

      case 3: // Location
        if (_selectedLocation == null) {
          _showErrorSnackBar(s.locationRequired);
          return false;
        }
        break;

      case 4: // Gallery
        if (_imageGallery.isEmpty) {
          _showErrorSnackBar(s.imagesRequired);
          return false;
        }
        if (_imageGallery.length < 3) {
          _showErrorSnackBar(s.imagesMinimum);
          return false;
        }
        break;
    }

    return true;
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.only(bottom: 100, left: 16, right: 16),
      ),
    );
    HapticFeedback.lightImpact();
  }

  void _showValidationErrors() {
    // Additional validation error handling if needed
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() => _currentStep--);
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _submitForm() async {
    final s = S.of(context);

    if (!_validateCurrentStep()) {
      _showErrorSnackBar(s.validationFailed);
      return;
    }

    // Show loading
    _showLoadingDialog(s.savingProperty);

    try {
      // Collect all form data
      final propertyData = {
        'title': _titleController.text.trim(),
        'content': _descriptionController.text.trim(),
        'price': double.parse(_priceController.text),
        'no_guests': int.parse(_guestsController.text),
        'beds': int.parse(_bedsController.text),
        'baths': int.parse(_bathsController.text),
        'service_category_id': int.parse(_selectedCategory!),
        'property_type_id': int.parse(_selectedPropertyType!),
        'cancellation_policy_id': int.parse(_selectedCancellationPolicy!),
        'facility_ids': _selectedFacilities,
        'lat': _selectedLocation?.latitude ?? 24.7136,
        'lon': _selectedLocation?.longitude ?? 46.6753,
        'address': _locationAddress ?? 'Riyadh, Saudi Arabia',
      };

      // Create property using production API service
      final apiService = getIt<PropertyCreationApiService>();

      final createdProperty = await apiService.createProperty(
        propertyData: propertyData,
        images: _imageGallery.isNotEmpty ? _imageGallery : null,
        video: _videoFile,
      );

      if (mounted) {
        Navigator.pop(context); // Close loading dialog

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text(s.propertyCreatedSuccessfully),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );

        // Navigate back with success result
        context.pop(createdProperty);
      }

    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        _showErrorSnackBar('Failed to create property: $e');
      }
    }
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Expanded(child: Text(message)),
          ],
        ),
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return EnhancedPageLayout(
      title: s.createProperty,
      body: BlocConsumer<PropertyCreationCubit, PropertyCreationState>(
        listener: (context, state) {
          if (state is PropertyCreationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(s.propertyCreatedSuccessfully),
                backgroundColor: Colors.green,
              ),
            );
            context.pop(true);
          } else if (state is PropertyCreationError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is PropertyCreationLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return Column(
            children: [
              // Progress indicator
              _buildProgressIndicator(),
              
              // Form content
              Expanded(
                child: PageView(
                  controller: _pageController,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    _buildBasicInfoStep(state),
                    _buildCategoryStep(state),
                    _buildDetailsStep(state),
                    _buildLocationStep(state),
                    _buildGalleryStep(state),
                    _buildReviewStep(),
                  ],
                ),
              ),
              
              // Navigation buttons
              _buildNavigationButtons(state),
            ],
          );
        },
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: List.generate(_totalSteps, (index) {
          final isActive = index <= _currentStep;
          final isCompleted = index < _currentStep;
          
          return Expanded(
            child: Container(
              height: 4,
              margin: EdgeInsets.only(right: index < _totalSteps - 1 ? 8 : 0),
              decoration: BoxDecoration(
                color: isCompleted 
                    ? Colors.green 
                    : isActive 
                        ? const Color(0xFFFEC53A) 
                        : Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildBasicInfoStep(PropertyCreationState state) {
    final s = S.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Enhanced header with icon
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFFFEC53A), Color(0xFFFFD700)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(Icons.info_outline, color: Colors.white, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        s.basicInformation,
                        style: AppTextStyles.font18Bold.copyWith(color: Colors.white),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        s.basicInformationDesc ?? 'Tell us about your property',
                        style: AppTextStyles.font14Regular.copyWith(color: Colors.white.withValues(alpha: 0.9)),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Enhanced form fields
          _buildEnhancedTextField(
            controller: _titleController,
            label: s.propertyTitle ?? 'Property Title',
            hint: s.propertyTitleHint ?? 'Enter a catchy title for your property',
            icon: Icons.title,
            maxLength: 100,
          ),
          const SizedBox(height: 20),

          _buildEnhancedTextField(
            controller: _descriptionController,
            label: s.propertyDescription ?? 'Description',
            hint: s.propertyDescriptionHint ?? 'Describe your property in detail',
            icon: Icons.description,
            maxLines: 4,
            maxLength: 500,
          ),
          const SizedBox(height: 20),

          _buildEnhancedTextField(
            controller: _priceController,
            label: s.pricePerNight ?? 'Price per Night (SAR)',
            hint: s.priceHint ?? '100',
            icon: Icons.attach_money,
            keyboardType: TextInputType.number,
            prefixText: 'SAR ',
          ),

          const SizedBox(height: 16),

          // Price guidance card
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.lightbulb_outline, color: Colors.blue, size: 20),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    s.priceGuidance ?? 'Tip: Research similar properties in your area to set competitive pricing',
                    style: AppTextStyles.font12Regular.copyWith(color: Colors.blue[700]),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryStep(PropertyCreationState state) {
    final s = S.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Enhanced header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(Icons.category, color: Colors.white, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        s.categoryAndType,
                        style: AppTextStyles.font18Bold.copyWith(color: Colors.white),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Choose the category and type that best describes your property',
                        style: AppTextStyles.font14Regular.copyWith(color: Colors.white.withValues(alpha: 0.9)),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Enhanced selections
          _buildEnhancedCategorySelection(state),
          const SizedBox(height: 20),

          _buildEnhancedPropertyTypeSelection(state),
          const SizedBox(height: 20),

          _buildEnhancedCancellationPolicySelection(state),
        ],
      ),
    );
  }

  Widget _buildDetailsStep(PropertyCreationState state) {
    final s = S.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.propertyDetails,
            style: AppTextStyles.font20Bold,
          ),
          const SizedBox(height: 24),
          
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _guestsController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: s.maxGuests,
                    border: const OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _bedsController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'Bedrooms',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _bathsController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'Bathrooms',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          
          // Facilities selection
          _buildFacilitiesSelection(state),
        ],
      ),
    );
  }

  Widget _buildReviewStep() {
    final s = S.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.reviewAndSubmit,
            style: AppTextStyles.font20Bold,
          ),
          const SizedBox(height: 24),
          
          _buildReviewCard('Title', _titleController.text),
          _buildReviewCard('Description', _descriptionController.text),
          _buildReviewCard('Price', '${_priceController.text} SAR/night'),
          _buildReviewCard('Guests', _guestsController.text),
          _buildReviewCard('Bedrooms', _bedsController.text),
          _buildReviewCard('Bathrooms', _bathsController.text),
          _buildReviewCard('Category', _selectedCategory ?? 'Not selected'),
          _buildReviewCard('Property Type', _selectedPropertyType ?? 'Not selected'),
          _buildReviewCard('Cancellation Policy', _selectedCancellationPolicy ?? 'Not selected'),
          _buildReviewCard('Facilities', _getSelectedFacilitiesNames()),
          _buildReviewCard('Location', _locationAddress ?? 'Not selected'),
          _buildReviewCard('Photos', '${_imageGallery.length} images'),
          _buildReviewCard('Video', _videoFile != null ? 'Added' : 'Not added'),
        ],
      ),
    );
  }

  Widget _buildLocationStep(PropertyCreationState state) {
    final s = S.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.locationAndAddress,
            style: AppTextStyles.font20Bold,
          ),
          const SizedBox(height: 24),

          // Location picker
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.location_on, color: Color(0xFFFEC53A)),
                      const SizedBox(width: 8),
                      Text(s.propertyLocation),
                      const Spacer(),
                      if (_selectedLocation != null)
                        const Icon(Icons.check_circle, color: Colors.green),
                    ],
                  ),
                  const SizedBox(height: 16),

                  if (_selectedLocation != null) ...[
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.check_circle, color: Colors.green, size: 20),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(s.locationSelected, style: const TextStyle(fontWeight: FontWeight.bold)),
                                if (_locationAddress != null)
                                  Text(_locationAddress!, style: const TextStyle(fontSize: 12)),
                                Text(
                                  'Lat: ${_selectedLocation!.latitude.toStringAsFixed(6)}, '
                                  'Lng: ${_selectedLocation!.longitude.toStringAsFixed(6)}',
                                  style: const TextStyle(fontSize: 10, color: Colors.grey),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  ElevatedButton.icon(
                    onPressed: _openLocationPicker,
                    icon: Icon(_selectedLocation != null ? Icons.edit_location : Icons.add_location),
                    label: Text(_selectedLocation != null ? s.changeLocation : s.selectLocation),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFEC53A),
                      foregroundColor: Colors.black,
                      minimumSize: const Size(double.infinity, 48),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGalleryStep(PropertyCreationState state) {
    final s = S.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.photosAndVideo,
            style: AppTextStyles.font20Bold,
          ),
          const SizedBox(height: 24),

          // Image gallery section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.photo_library, color: Color(0xFFFEC53A)),
                      const SizedBox(width: 8),
                      Text(s.propertyPhotos),
                      const Spacer(),
                      Text('${_imageGallery.length}/10', style: const TextStyle(color: Colors.grey)),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Image grid
                  if (_imageGallery.isNotEmpty) ...[
                    GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 8,
                      ),
                      itemCount: _imageGallery.length + 1,
                      itemBuilder: (context, index) {
                        if (index == _imageGallery.length) {
                          return _buildAddImageButton();
                        }
                        return _buildImageItem(index);
                      },
                    ),
                  ] else ...[
                    _buildEmptyGalleryState(),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Video section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.videocam, color: Color(0xFFFEC53A)),
                      const SizedBox(width: 8),
                      Text(s.propertyVideoOptional),
                      const Spacer(),
                      if (_videoFile != null)
                        const Icon(Icons.check_circle, color: Colors.green),
                    ],
                  ),
                  const SizedBox(height: 16),

                  if (_videoFile != null) ...[
                    _buildVideoPreview(),
                    const SizedBox(height: 16),
                  ],

                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: _pickVideo,
                          icon: const Icon(Icons.video_library),
                          label: Text(_videoFile != null ? 'Change Video' : 'Add Video'),
                        ),
                      ),
                      if (_videoFile != null) ...[
                        const SizedBox(width: 8),
                        IconButton(
                          onPressed: _removeVideo,
                          icon: const Icon(Icons.delete, color: Colors.red),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewCard(String label, String value) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: 100,
              child: Text(
                label,
                style: AppTextStyles.font14SemiBold,
              ),
            ),
            Expanded(
              child: Text(
                value.isEmpty ? 'Not provided' : value,
                style: AppTextStyles.font14Regular,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationButtons(PropertyCreationState state) {
    final s = S.of(context);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousStep,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: const BorderSide(color: Color(0xFFFEC53A)),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.arrow_back, color: Color(0xFFFEC53A), size: 18),
                    const SizedBox(width: 8),
                    Text(
                      s.previous,
                      style: AppTextStyles.font14SemiBold.copyWith(color: const Color(0xFFFEC53A)),
                    ),
                  ],
                ),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: state is PropertyCreationLoading
                  ? null
                  : _currentStep == _totalSteps - 1
                      ? _submitForm
                      : _nextStep,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFEC53A),
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                elevation: 2,
              ),
              child: state is PropertyCreationLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                      ),
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          _currentStep == _totalSteps - 1 ? s.createProperty : s.next,
                          style: AppTextStyles.font14SemiBold,
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          _currentStep == _totalSteps - 1 ? Icons.check : Icons.arrow_forward,
                          size: 18,
                        ),
                      ],
                    ),
            ),
          ),
        ],
      ),
    );
  }

  // Enhanced selection methods
  Widget _buildEnhancedCategorySelection(PropertyCreationState state) {
    final s = S.of(context);
    final categories = [
      {'id': '1', 'name': 'Apartments', 'name_ar': 'شقق', 'icon': Icons.apartment},
      {'id': '2', 'name': 'Houses', 'name_ar': 'منازل', 'icon': Icons.house},
      {'id': '3', 'name': 'Villas', 'name_ar': 'فلل', 'icon': Icons.villa},
      {'id': '4', 'name': 'Studios', 'name_ar': 'استوديوهات', 'icon': Icons.home},
    ];

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFEC53A).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Icon(Icons.category, color: Color(0xFFFEC53A), size: 20),
                ),
                const SizedBox(width: 12),
                Text(s.selectCategory, style: AppTextStyles.font16SemiBold),
              ],
            ),
            const SizedBox(height: 16),

            // Category grid
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 2.5,
              ),
              itemCount: categories.length,
              itemBuilder: (context, index) {
                final category = categories[index];
                final isSelected = _selectedCategory == category['id'];

                return GestureDetector(
                  onTap: () {
                    setState(() => _selectedCategory = category['id'] as String);
                    HapticFeedback.selectionClick();
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: isSelected ? const Color(0xFFFEC53A).withValues(alpha: 0.1) : Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected ? const Color(0xFFFEC53A) : Colors.grey.withValues(alpha: 0.3),
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          category['icon'] as IconData,
                          color: isSelected ? const Color(0xFFFEC53A) : Colors.grey[600],
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          category['name'] as String,
                          style: AppTextStyles.font14SemiBold.copyWith(
                            color: isSelected ? const Color(0xFFFEC53A) : Colors.grey[700],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedPropertyTypeSelection(PropertyCreationState state) {
    final s = S.of(context);
    final propertyTypes = PropertyType.getDefaultTypes();

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFEC53A).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Icon(Icons.home_work, color: Color(0xFFFEC53A), size: 20),
                ),
                const SizedBox(width: 12),
                Text(s.propertyType, style: AppTextStyles.font16SemiBold),
              ],
            ),
            const SizedBox(height: 16),

            // Property type list
            ...propertyTypes.map((type) {
              final isSelected = _selectedPropertyType == type.id.toString();

              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                child: GestureDetector(
                  onTap: () {
                    setState(() => _selectedPropertyType = type.id.toString());
                    HapticFeedback.selectionClick();
                  },
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isSelected ? const Color(0xFFFEC53A).withValues(alpha: 0.1) : Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected ? const Color(0xFFFEC53A) : Colors.grey.withValues(alpha: 0.3),
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: isSelected ? const Color(0xFFFEC53A) : Colors.grey[400],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.home,
                            color: isSelected ? Colors.white : Colors.grey[600],
                            size: 16,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                type.name,
                                style: AppTextStyles.font14SemiBold.copyWith(
                                  color: isSelected ? const Color(0xFFFEC53A) : Colors.grey[700],
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                type.description,
                                style: AppTextStyles.font12Regular.copyWith(color: Colors.grey[600]),
                              ),
                            ],
                          ),
                        ),
                        if (isSelected)
                          const Icon(Icons.check_circle, color: Color(0xFFFEC53A), size: 20),
                      ],
                    ),
                  ),
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedCancellationPolicySelection(PropertyCreationState state) {
    final s = S.of(context);
    final policies = CancellationPolicy.getDefaultPolicies();

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFEC53A).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Icon(Icons.policy, color: Color(0xFFFEC53A), size: 20),
                ),
                const SizedBox(width: 12),
                Text(s.cancellationPolicy, style: AppTextStyles.font16SemiBold),
              ],
            ),
            const SizedBox(height: 16),

            // Policy list
            ...policies.map((policy) {
              final isSelected = _selectedCancellationPolicy == policy.id.toString();

              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                child: GestureDetector(
                  onTap: () {
                    setState(() => _selectedCancellationPolicy = policy.id.toString());
                    HapticFeedback.selectionClick();
                  },
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isSelected ? const Color(0xFFFEC53A).withValues(alpha: 0.1) : Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected ? const Color(0xFFFEC53A) : Colors.grey.withValues(alpha: 0.3),
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: isSelected ? const Color(0xFFFEC53A) : Colors.grey[400],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.schedule,
                            color: isSelected ? Colors.white : Colors.grey[600],
                            size: 16,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                policy.name,
                                style: AppTextStyles.font14SemiBold.copyWith(
                                  color: isSelected ? const Color(0xFFFEC53A) : Colors.grey[700],
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                policy.description,
                                style: AppTextStyles.font12Regular.copyWith(color: Colors.grey[600]),
                              ),
                            ],
                          ),
                        ),
                        if (isSelected)
                          const Icon(Icons.check_circle, color: Color(0xFFFEC53A), size: 20),
                      ],
                    ),
                  ),
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildFacilitiesSelection(PropertyCreationState state) {
    final facilities = PropertyFacility.getDefaultFacilities();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Select Facilities'),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: facilities.map((facility) {
                final isSelected = _selectedFacilities.contains(facility.id);
                return FilterChip(
                  label: Text(facility.name),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _selectedFacilities.add(facility.id);
                      } else {
                        _selectedFacilities.remove(facility.id);
                      }
                    });
                  },
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  String _getSelectedFacilitiesNames() {
    if (_selectedFacilities.isEmpty) return 'None selected';

    final facilities = PropertyFacility.getDefaultFacilities();
    final selectedNames = _selectedFacilities
        .map((id) => facilities.firstWhere((f) => f.id == id, orElse: () => const PropertyFacility(
              id: 0, name: 'Unknown', nameAr: '', icon: '', category: '', categoryAr: '')).name)
        .toList();

    return selectedNames.join(', ');
  }

  // Location picker methods
  void _openLocationPicker() async {
    try {
      // For now, use a simple dialog to simulate location picker
      // In production, you would integrate with the actual OpenStreetMapPickerScreen
      final result = await showDialog<Map<String, dynamic>>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Select Location'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Location picker will be integrated here.'),
              SizedBox(height: 16),
              Text('For demo purposes, using default Riyadh location.'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, {
                'lat': 24.7136,
                'lng': 46.6753,
                'address': 'Riyadh, Saudi Arabia',
              }),
              child: const Text('Use Default Location'),
            ),
          ],
        ),
      );

      if (result != null) {
        setState(() {
          _selectedLocation = LatLng(result['lat'], result['lng']);
          _locationAddress = result['address'];
        });

        // Auto-save location data
        _performAutoSave();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to open location picker: $e')),
        );
      }
    }
  }

  // Gallery methods
  Widget _buildAddImageButton() {
    return GestureDetector(
      onTap: _showImagePickerOptions,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300]!, style: BorderStyle.solid),
        ),
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add_photo_alternate, color: Colors.grey, size: 32),
            SizedBox(height: 4),
            Text('Add Photo', style: TextStyle(color: Colors.grey, fontSize: 12)),
          ],
        ),
      ),
    );
  }

  Widget _buildImageItem(int index) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            image: DecorationImage(
              image: FileImage(_imageGallery[index]),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
            onTap: () => _removeImage(index),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.close, color: Colors.white, size: 16),
            ),
          ),
        ),
        if (index == 0)
          Positioned(
            bottom: 4,
            left: 4,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: const BoxDecoration(
                color: Color(0xFFFEC53A),
                borderRadius: BorderRadius.all(Radius.circular(4)),
              ),
              child: const Text('Main', style: TextStyle(fontSize: 10, fontWeight: FontWeight.bold)),
            ),
          ),
      ],
    );
  }

  Widget _buildEmptyGalleryState() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!, style: BorderStyle.solid),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.photo_library_outlined, color: Colors.grey, size: 48),
          const SizedBox(height: 8),
          const Text('No photos added yet', style: TextStyle(color: Colors.grey)),
          const SizedBox(height: 8),
          ElevatedButton.icon(
            onPressed: _showImagePickerOptions,
            icon: const Icon(Icons.add_photo_alternate),
            label: const Text('Add Photos'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFEC53A),
              foregroundColor: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  void _showImagePickerOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Take Photo'),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromCamera();
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('Choose from Gallery'),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromGallery();
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library_outlined),
              title: const Text('Choose Multiple'),
              onTap: () {
                Navigator.pop(context);
                _pickMultipleImages();
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImageFromCamera() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(source: ImageSource.camera);
      if (pickedFile != null) {
        setState(() {
          _imageGallery.add(File(pickedFile.path));
        });
        _performAutoSave();
        HapticFeedback.selectionClick();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to take photo: $e')),
        );
      }
    }
  }

  Future<void> _pickImageFromGallery() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(source: ImageSource.gallery);
      if (pickedFile != null) {
        setState(() {
          _imageGallery.add(File(pickedFile.path));
        });
        _performAutoSave();
        HapticFeedback.selectionClick();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to pick image: $e')),
        );
      }
    }
  }

  Future<void> _pickMultipleImages() async {
    try {
      final List<XFile> pickedFiles = await _picker.pickMultiImage();
      if (pickedFiles.isNotEmpty) {
        setState(() {
          for (final file in pickedFiles) {
            if (_imageGallery.length < 10) {
              _imageGallery.add(File(file.path));
            }
          }
        });
        _performAutoSave();
        HapticFeedback.selectionClick();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to pick images: $e')),
        );
      }
    }
  }

  void _removeImage(int index) {
    setState(() {
      _imageGallery.removeAt(index);
    });
    _performAutoSave();
    HapticFeedback.lightImpact();
  }

  // Video methods
  Widget _buildVideoPreview() {
    if (_videoController != null && _videoController!.value.isInitialized) {
      return Container(
        height: 200,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.black,
        ),
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: AspectRatio(
                aspectRatio: _videoController!.value.aspectRatio,
                child: VideoPlayer(_videoController!),
              ),
            ),
            Positioned(
              bottom: 8,
              right: 8,
              child: FloatingActionButton.small(
                onPressed: () {
                  setState(() {
                    _videoController!.value.isPlaying
                        ? _videoController!.pause()
                        : _videoController!.play();
                  });
                },
                child: Icon(
                  _videoController!.value.isPlaying ? Icons.pause : Icons.play_arrow,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.video_library, size: 48, color: Colors.grey),
            SizedBox(height: 8),
            Text('Video Preview', style: TextStyle(color: Colors.grey)),
          ],
        ),
      ),
    );
  }

  Future<void> _pickVideo() async {
    try {
      final XFile? pickedFile = await _picker.pickVideo(source: ImageSource.gallery);
      if (pickedFile != null) {
        setState(() {
          _videoFile = File(pickedFile.path);
        });

        // Initialize video controller
        _videoController?.dispose();
        _videoController = VideoPlayerController.file(_videoFile!)
          ..initialize().then((_) {
            setState(() {});
          });

        _performAutoSave();
        HapticFeedback.selectionClick();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to pick video: $e')),
        );
      }
    }
  }

  void _removeVideo() {
    setState(() {
      _videoFile = null;
      _videoController?.dispose();
      _videoController = null;
    });
    _performAutoSave();
    HapticFeedback.lightImpact();
  }

  // Enhanced UI Components
  Widget _buildEnhancedTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    int maxLines = 1,
    int? maxLength,
    TextInputType? keyboardType,
    String? prefixText,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        maxLines: maxLines,
        maxLength: maxLength,
        keyboardType: keyboardType,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          prefixText: prefixText,
          prefixIcon: Container(
            margin: const EdgeInsets.all(12),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFFFEC53A).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: const Color(0xFFFEC53A), size: 20),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFFFEC53A), width: 2),
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
        style: AppTextStyles.font16Regular,
      ),
    );
  }

}
