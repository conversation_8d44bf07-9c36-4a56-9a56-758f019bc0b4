import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/feature/host/presentation/cubit/property_creation_cubit.dart';
import 'package:gather_point/feature/dashboard/data/services/properties_api_service.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'package:gather_point/feature/host/data/models/property_type_model.dart';
import 'package:gather_point/feature/cancellation_policies/data/models/cancellation_policy_model.dart';
import 'package:gather_point/core/services/service_locator.dart';

class CreatePropertyPage extends StatelessWidget {
  const CreatePropertyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => PropertyCreationCubit(getIt<PropertiesApiService>())
        ..loadInitialData(),
      child: const _CreatePropertyPageContent(),
    );
  }
}

class _CreatePropertyPageContent extends StatefulWidget {
  const _CreatePropertyPageContent();

  @override
  State<_CreatePropertyPageContent> createState() => _CreatePropertyPageContentState();
}

class _CreatePropertyPageContentState extends State<_CreatePropertyPageContent> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _weekendPriceController = TextEditingController();
  final _weekPriceController = TextEditingController();
  final _monthPriceController = TextEditingController();
  final _guestsController = TextEditingController();
  final _bedsController = TextEditingController();
  final _bathsController = TextEditingController();
  final _bookingRulesController = TextEditingController();
  final _cancellationRulesController = TextEditingController();

  ServiceCategory? _selectedCategory;
  PropertyTypeModel? _selectedPropertyType;
  CancellationPolicyModel? _selectedCancellationPolicy;
  final List<int> _selectedFacilities = [];
  File? _mainImage;
  File? _video;
  final List<File> _galleryImages = [];
  double? _latitude;
  double? _longitude;

  final ImagePicker _picker = ImagePicker();

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _weekendPriceController.dispose();
    _weekPriceController.dispose();
    _monthPriceController.dispose();
    _guestsController.dispose();
    _bedsController.dispose();
    _bathsController.dispose();
    _bookingRulesController.dispose();
    _cancellationRulesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return EnhancedPageLayout(
      title: s.createProperty,
      body: BlocConsumer<PropertyCreationCubit, PropertyCreationState>(
        listener: (context, state) {
          if (state is PropertyCreationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(s.propertyCreatedSuccessfully),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.of(context).pop(true); // Return true to indicate success
          } else if (state is PropertyCreationError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is PropertyCreationLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Basic Information Section
                  _buildSectionHeader(s.basicInformation),
                  const SizedBox(height: 16),
                  
                  _buildTextField(
                    controller: _titleController,
                    label: s.propertyTitle,
                    hint: s.enterPropertyTitle,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return s.pleaseEnterPropertyTitle;
                      }
                      return null;
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildTextField(
                    controller: _descriptionController,
                    label: s.description,
                    hint: s.enterPropertyDescription,
                    maxLines: 4,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return s.pleaseEnterDescription;
                      }
                      return null;
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Category Selection
                  _buildCategoryDropdown(state),

                  const SizedBox(height: 16),

                  // Property Type Selection
                  _buildPropertyTypeDropdown(state),

                  const SizedBox(height: 16),

                  // Cancellation Policy Selection
                  _buildCancellationPolicyDropdown(state),

                  const SizedBox(height: 24),
                  
                  // Pricing Section
                  _buildSectionHeader(s.pricing),
                  const SizedBox(height: 16),
                  
                  Row(
                    children: [
                      Expanded(
                        child: _buildTextField(
                          controller: _priceController,
                          label: s.dailyPrice,
                          hint: '0.00',
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return s.pleaseEnterPrice;
                            }
                            if (double.tryParse(value) == null) {
                              return s.pleaseEnterValidPrice;
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildTextField(
                          controller: _weekendPriceController,
                          label: s.weekendPrice,
                          hint: '0.00',
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  Row(
                    children: [
                      Expanded(
                        child: _buildTextField(
                          controller: _weekPriceController,
                          label: s.weeklyPrice,
                          hint: '0.00',
                          keyboardType: TextInputType.number,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildTextField(
                          controller: _monthPriceController,
                          label: s.monthlyPrice,
                          hint: '0.00',
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Property Details Section
                  _buildSectionHeader(s.propertyDetails),
                  const SizedBox(height: 16),
                  
                  Row(
                    children: [
                      Expanded(
                        child: _buildTextField(
                          controller: _guestsController,
                          label: s.maxGuests,
                          hint: '1',
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return s.pleaseEnterMaxGuests;
                            }
                            if (int.tryParse(value) == null) {
                              return s.pleaseEnterValidNumber;
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildTextField(
                          controller: _bedsController,
                          label: s.bedrooms,
                          hint: '1',
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return s.pleaseEnterBedrooms;
                            }
                            if (int.tryParse(value) == null) {
                              return s.pleaseEnterValidNumber;
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildTextField(
                          controller: _bathsController,
                          label: s.bathrooms,
                          hint: '1',
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return s.pleaseBathrooms;
                            }
                            if (int.tryParse(value) == null) {
                              return s.pleaseEnterValidNumber;
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Facilities Section
                  _buildFacilitiesSection(state),
                  
                  const SizedBox(height: 24),
                  
                  // Location Section
                  _buildLocationSection(),

                  const SizedBox(height: 24),

                  // Media Section
                  _buildMediaSection(),

                  const SizedBox(height: 24),

                  // Rules Section
                  _buildRulesSection(),
                  
                  const SizedBox(height: 32),
                  
                  // Submit Button
                  SizedBox(
                    width: double.infinity,
                    child: EnhancedButton(
                      text: s.createProperty,
                      onPressed: state is PropertyCreationLoading ? null : _submitForm,
                      icon: Icons.add_home_rounded,
                    ),
                  ),
                  
                  const SizedBox(height: 100), // Bottom padding
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: AppTextStyles.font18Bold.copyWith(
        color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      keyboardType: keyboardType,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: const OutlineInputBorder(),
        filled: true,
        fillColor: Colors.grey.withValues(alpha: 0.05),
      ),
    );
  }

  Widget _buildCategoryDropdown(PropertyCreationState state) {
    final s = S.of(context);

    if (state is PropertyCreationDataLoaded) {
      return DropdownButtonFormField<ServiceCategory>(
        value: _selectedCategory,
        decoration: InputDecoration(
          labelText: s.category,
          border: const OutlineInputBorder(),
          filled: true,
          fillColor: Colors.grey.withValues(alpha: 0.05),
        ),
        items: state.categories.map((category) {
          return DropdownMenuItem<ServiceCategory>(
            value: category,
            child: Text(category.title),
          );
        }).toList(),
        onChanged: (ServiceCategory? value) {
          setState(() {
            _selectedCategory = value;
          });
        },
        validator: (value) {
          if (value == null) {
            return s.pleaseSelectCategory;
          }
          return null;
        },
      );
    }

    return const CircularProgressIndicator();
  }

  Widget _buildPropertyTypeDropdown(PropertyCreationState state) {
    final s = S.of(context);

    if (state is PropertyCreationDataLoaded) {
      return DropdownButtonFormField<PropertyTypeModel>(
        value: _selectedPropertyType,
        decoration: InputDecoration(
          labelText: s.propertyType,
          border: const OutlineInputBorder(),
          filled: true,
          fillColor: Colors.grey.withValues(alpha: 0.05),
        ),
        items: state.propertyTypes.map((propertyType) {
          return DropdownMenuItem<PropertyTypeModel>(
            value: propertyType,
            child: Text(propertyType.title),
          );
        }).toList(),
        onChanged: (PropertyTypeModel? value) {
          setState(() {
            _selectedPropertyType = value;
          });
        },
        validator: (value) {
          if (value == null) {
            return s.pleaseSelectPropertyType;
          }
          return null;
        },
      );
    }

    return const CircularProgressIndicator();
  }

  Widget _buildCancellationPolicyDropdown(PropertyCreationState state) {
    final s = S.of(context);

    if (state is PropertyCreationDataLoaded) {
      return DropdownButtonFormField<CancellationPolicyModel>(
        value: _selectedCancellationPolicy,
        decoration: InputDecoration(
          labelText: s.cancellationPolicy,
          border: const OutlineInputBorder(),
          filled: true,
          fillColor: Colors.grey.withValues(alpha: 0.05),
        ),
        items: state.cancellationPolicies.map((policy) {
          return DropdownMenuItem<CancellationPolicyModel>(
            value: policy,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  policy.name,
                  style: AppTextStyles.font14SemiBold,
                ),
                Text(
                  policy.description,
                  style: AppTextStyles.font12Regular.copyWith(
                    color: Colors.grey[600],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          );
        }).toList(),
        onChanged: (CancellationPolicyModel? value) {
          setState(() {
            _selectedCancellationPolicy = value;
          });
        },
        validator: (value) {
          if (value == null) {
            return s.pleaseSelectCancellationPolicy;
          }
          return null;
        },
      );
    }

    return const CircularProgressIndicator();
  }

  Widget _buildFacilitiesSection(PropertyCreationState state) {
    final s = S.of(context);

    if (state is PropertyCreationDataLoaded) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(s.facilities),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: state.facilities.map((facility) {
              final isSelected = _selectedFacilities.contains(facility.id);
              return FilterChip(
                label: Text(facility.title),
                selected: isSelected,
                onSelected: (bool selected) {
                  setState(() {
                    if (selected) {
                      _selectedFacilities.add(facility.id);
                    } else {
                      _selectedFacilities.remove(facility.id);
                    }
                  });
                },
                selectedColor: Colors.blue.withValues(alpha: 0.2),
                checkmarkColor: Colors.blue,
              );
            }).toList(),
          ),
        ],
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildMediaSection() {
    final s = S.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(s.media),
        const SizedBox(height: 16),

        // Main Image
        _buildImagePicker(
          title: s.mainImage,
          image: _mainImage,
          onTap: () => _pickMainImage(),
        ),

        const SizedBox(height: 16),

        // Video
        _buildVideoPicker(),

        const SizedBox(height: 16),

        // Gallery Images
        _buildGalleryPicker(),
      ],
    );
  }

  Widget _buildImagePicker({
    required String title,
    required File? image,
    required VoidCallback onTap,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTextStyles.font14SemiBold,
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: onTap,
          child: Container(
            height: 120,
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
              color: Colors.grey.withValues(alpha: 0.1),
            ),
            child: image != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      image,
                      fit: BoxFit.cover,
                    ),
                  )
                : const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.add_photo_alternate, size: 48, color: Colors.grey),
                      SizedBox(height: 8),
                      Text('اضغط لإضافة صورة'),
                    ],
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildVideoPicker() {
    final s = S.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          s.video,
          style: AppTextStyles.font14SemiBold,
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: _pickVideo,
          child: Container(
            height: 80,
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
              color: Colors.grey.withValues(alpha: 0.1),
            ),
            child: _video != null
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.video_file, size: 32, color: Colors.blue),
                      const SizedBox(width: 8),
                      Text(_video!.path.split('/').last),
                    ],
                  )
                : const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.video_call, size: 32, color: Colors.grey),
                      SizedBox(height: 4),
                      Text('اضغط لإضافة فيديو'),
                    ],
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildGalleryPicker() {
    final s = S.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              s.gallery,
              style: AppTextStyles.font14SemiBold,
            ),
            TextButton.icon(
              onPressed: _pickGalleryImages,
              icon: const Icon(Icons.add_photo_alternate),
              label: Text(s.addImages),
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (_galleryImages.isNotEmpty)
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _galleryImages.length,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.file(
                          _galleryImages[index],
                          width: 100,
                          height: 100,
                          fit: BoxFit.cover,
                        ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _galleryImages.removeAt(index);
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          )
        else
          Container(
            height: 100,
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
              color: Colors.grey.withValues(alpha: 0.1),
            ),
            child: const Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.photo_library, size: 32, color: Colors.grey),
                SizedBox(height: 4),
                Text('لا توجد صور في المعرض'),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildLocationSection() {
    final s = S.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(s.location),
        const SizedBox(height: 16),

        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey.withValues(alpha: 0.05),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Row(
                children: [
                  Icon(Icons.location_on, color: Colors.blue),
                  SizedBox(width: 8),
                  Text(
                    'موقع العقار',
                    style: AppTextStyles.font14SemiBold,
                  ),
                ],
              ),
              const SizedBox(height: 12),

              if (_latitude != null && _longitude != null)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.check_circle, color: Colors.green, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'تم تحديد الموقع: ${_latitude!.toStringAsFixed(6)}, ${_longitude!.toStringAsFixed(6)}',
                          style: AppTextStyles.font12Regular.copyWith(
                            color: Colors.green[700],
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              else
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.warning, color: Colors.orange, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'لم يتم تحديد الموقع بعد',
                          style: AppTextStyles.font12Regular.copyWith(
                            color: Colors.orange[700],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              const SizedBox(height: 16),

              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _pickLocation,
                  icon: const Icon(Icons.map),
                  label: Text(_latitude != null && _longitude != null
                      ? 'تغيير الموقع'
                      : 'اختيار الموقع'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRulesSection() {
    final s = S.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(s.rules),
        const SizedBox(height: 16),

        _buildTextField(
          controller: _bookingRulesController,
          label: s.bookingRules,
          hint: s.enterBookingRules,
          maxLines: 3,
        ),

        const SizedBox(height: 16),

        _buildTextField(
          controller: _cancellationRulesController,
          label: s.cancellationRules,
          hint: s.enterCancellationRules,
          maxLines: 3,
        ),
      ],
    );
  }

  Future<void> _pickMainImage() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        _mainImage = File(image.path);
      });
    }
  }

  Future<void> _pickVideo() async {
    final XFile? video = await _picker.pickVideo(source: ImageSource.gallery);
    if (video != null) {
      setState(() {
        _video = File(video.path);
      });
    }
  }

  Future<void> _pickGalleryImages() async {
    final List<XFile> images = await _picker.pickMultiImage();
    if (images.isNotEmpty) {
      setState(() {
        _galleryImages.addAll(images.map((image) => File(image.path)));
      });
    }
  }

  Future<void> _pickLocation() async {
    // For now, use dummy coordinates (Riyadh center)
    // In a real app, you would integrate with a map picker
    setState(() {
      _latitude = 24.7136;
      _longitude = 46.6753;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديد الموقع بنجاح'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      if (_selectedCategory == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يرجى اختيار فئة العقار'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      if (_selectedPropertyType == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يرجى اختيار نوع العقار'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      if (_selectedCancellationPolicy == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يرجى اختيار سياسة الإلغاء'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final propertyData = {
        'title': _titleController.text,
        'content': _descriptionController.text,
        'service_category_id': _selectedCategory!.id,
        'property_type_id': _selectedPropertyType!.id,
        'cancellation_policy_id': _selectedCancellationPolicy!.id,
        'price': double.parse(_priceController.text),
        'weekend_price': _weekendPriceController.text.isNotEmpty
            ? double.parse(_weekendPriceController.text) : null,
        'week_price': _weekPriceController.text.isNotEmpty
            ? double.parse(_weekPriceController.text) : null,
        'month_price': _monthPriceController.text.isNotEmpty
            ? double.parse(_monthPriceController.text) : null,
        'no_guests': int.parse(_guestsController.text),
        'beds': int.parse(_bedsController.text),
        'baths': int.parse(_bathsController.text),
        'booking_rules': _bookingRulesController.text.isNotEmpty
            ? _bookingRulesController.text : null,
        'cancelation_rules': _cancellationRulesController.text.isNotEmpty
            ? _cancellationRulesController.text : null,
        'facility_ids': _selectedFacilities,
        'lat': _latitude,
        'lon': _longitude,
      };

      context.read<PropertyCreationCubit>().createProperty(
        propertyData: propertyData,
        mainImage: _mainImage,
        video: _video,
        galleryImages: _galleryImages,
      );
    }
  }
}
