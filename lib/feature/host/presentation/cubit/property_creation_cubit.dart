import 'dart:io';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/feature/dashboard/data/services/properties_api_service.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'package:gather_point/feature/host/data/models/reservation_model.dart';
import 'package:gather_point/feature/host/data/models/property_type_model.dart';
import 'package:gather_point/feature/cancellation_policies/data/models/cancellation_policy_model.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';
import 'package:gather_point/feature/host/services/property_edit_service.dart';

part 'property_creation_state.dart';

class PropertyCreationCubit extends Cubit<PropertyCreationState> {
  final PropertiesApiService _propertiesApiService;
  final PropertyEditService? _propertyEditService;

  PropertyCreationCubit(this._propertiesApiService, [this._propertyEditService]) : super(PropertyCreationInitial());

  /// Safe emit that checks if cubit is not closed
  void _safeEmit(PropertyCreationState state) {
    if (!isClosed) {
      emit(state);
    }
  }

  /// Load initial data (categories, facilities, property types, and cancellation policies)
  Future<void> loadInitialData() async {
    _safeEmit(PropertyCreationLoading());

    try {
      // Load all required data in parallel
      final results = await Future.wait([
        _propertiesApiService.getServiceCategories(),
        _propertiesApiService.getFacilities(),
        _propertiesApiService.getPropertyTypes(),
        _propertiesApiService.getCancellationPolicies(),
      ]);

      final categories = results[0] as List<ServiceCategory>;
      final facilities = results[1] as List<FacilityModel>;
      final propertyTypes = results[2] as List<PropertyTypeModel>;
      final cancellationPolicies = results[3] as List<CancellationPolicyModel>;

      _safeEmit(PropertyCreationDataLoaded(
        categories: categories,
        facilities: facilities,
        propertyTypes: propertyTypes,
        cancellationPolicies: cancellationPolicies,
      ));
    } catch (e) {
      _safeEmit(PropertyCreationError(e.toString()));
    }
  }

  /// Create a new property
  Future<void> createProperty({
    required Map<String, dynamic> propertyData,
    File? mainImage,
    File? video,
    List<File>? galleryImages,
  }) async {
    _safeEmit(PropertyCreationLoading());

    try {
      final property = await _propertiesApiService.createProperty(
        title: propertyData['title'],
        content: propertyData['content'],
        serviceCategoryId: propertyData['service_category_id'],
        price: propertyData['price'],
        weekendPrice: propertyData['weekend_price'],
        weekPrice: propertyData['week_price'],
        monthPrice: propertyData['month_price'],
        lat: propertyData['lat'],
        lon: propertyData['lon'],
        noGuests: propertyData['no_guests'],
        beds: propertyData['beds'],
        baths: propertyData['baths'],
        bookingRules: propertyData['booking_rules'],
        cancelationRules: propertyData['cancelation_rules'],
        propertyTypeId: propertyData['property_type_id'],
        cancellationPolicyId: propertyData['cancellation_policy_id'],
        facilityIds: propertyData['facility_ids'],
        mainImage: mainImage,
        video: video,
        galleryImages: galleryImages,
      );

      _safeEmit(PropertyCreationSuccess(property));
    } catch (e) {
      _safeEmit(PropertyCreationError(e.toString()));
    }
  }

  /// Update an existing property
  Future<void> updateProperty({
    required int propertyId,
    required Map<String, dynamic> propertyData,
    File? mainImage,
    File? video,
  }) async {
    _safeEmit(PropertyCreationLoading());

    try {
      final property = await _propertiesApiService.updateProperty(
        propertyId: propertyId,
        title: propertyData['title'],
        content: propertyData['content'],
        serviceCategoryId: propertyData['service_category_id'],
        price: propertyData['price'],
        weekendPrice: propertyData['weekend_price'],
        weekPrice: propertyData['week_price'],
        monthPrice: propertyData['month_price'],
        lat: propertyData['lat'],
        lon: propertyData['lon'],
        noGuests: propertyData['no_guests'],
        beds: propertyData['beds'],
        baths: propertyData['baths'],
        bookingRules: propertyData['booking_rules'],
        cancelationRules: propertyData['cancelation_rules'],
        propertyTypeId: propertyData['property_type_id'],
        cancellationPolicyId: propertyData['cancellation_policy_id'],
        facilityIds: propertyData['facility_ids'],
        mainImage: mainImage,
        video: video,
      );

      _safeEmit(PropertyCreationSuccess(property));
    } catch (e) {
      _safeEmit(PropertyCreationError(e.toString()));
    }
  }

  /// Upload gallery images for a property
  Future<void> uploadGalleryImages(int propertyId, List<File> images) async {
    _safeEmit(PropertyCreationLoading());

    try {
      final success = await _propertiesApiService.uploadGalleryImages(propertyId, images);

      if (success) {
        _safeEmit(PropertyGalleryUploaded());
      } else {
        _safeEmit(PropertyCreationError('Failed to upload gallery images'));
      }
    } catch (e) {
      _safeEmit(PropertyCreationError(e.toString()));
    }
  }

  /// Load property data for editing
  Future<void> loadPropertyForEdit(int propertyId) async {
    _safeEmit(PropertyCreationLoading());
    try {
      // Load form data first
      await loadInitialData();

      // Then load property data
      final property = await (_propertyEditService?.getPropertyForEdit(propertyId) ??
          Future.value(PropertyEditService.generateMockPropertyData(propertyId)));

      // Get the current loaded state
      final currentState = state as PropertyCreationDataLoaded;

      _safeEmit(PropertyEditDataLoaded(
        property: _convertToPropertyItem(property),
        categories: currentState.categories,
        facilities: currentState.facilities,
        propertyTypes: currentState.propertyTypes,
        cancellationPolicies: currentState.cancellationPolicies,
      ));
    } catch (e) {
      _safeEmit(PropertyCreationError(e.toString()));
    }
  }

  /// Save property as draft
  Future<void> savePropertyAsDraft(int propertyId, Map<String, dynamic> propertyData) async {
    try {
      await _propertyEditService?.savePropertyAsDraft(propertyId, propertyData);
      _safeEmit(PropertyDraftSaved());
    } catch (e) {
      _safeEmit(PropertyCreationError(e.toString()));
    }
  }

  /// Initialize form for edit mode with existing data
  void initializeForEdit(MyListingModel propertyData) {
    // Load form data with edit mode flag
    if (state is PropertyCreationDataLoaded) {
      final currentState = state as PropertyCreationDataLoaded;
      _safeEmit(currentState.copyWith(
        isEditMode: true,
        existingProperty: _convertToPropertyItem(propertyData),
      ));
    }
  }

  /// Check if cubit is in edit mode
  bool get isEditMode {
    if (state is PropertyCreationDataLoaded) {
      return (state as PropertyCreationDataLoaded).isEditMode;
    }
    if (state is PropertyEditDataLoaded) {
      return true;
    }
    return false;
  }

  /// Get existing property data if in edit mode
  PropertyItemModel? get existingProperty {
    if (state is PropertyCreationDataLoaded) {
      return (state as PropertyCreationDataLoaded).existingProperty;
    }
    if (state is PropertyEditDataLoaded) {
      return (state as PropertyEditDataLoaded).property;
    }
    return null;
  }

  /// Reset to create mode
  void resetToCreateMode() {
    if (state is PropertyCreationDataLoaded) {
      final currentState = state as PropertyCreationDataLoaded;
      _safeEmit(currentState.copyWith(
        isEditMode: false,
        existingProperty: null,
      ));
    }
  }

  /// Convert MyListingModel to PropertyItemModel
  PropertyItemModel _convertToPropertyItem(MyListingModel listing) {
    return PropertyItemModel(
      id: listing.id,
      title: listing.title,
      content: listing.content,
      image: listing.mainImageUrl,
      price: listing.price,
      lat: listing.lat,
      lon: listing.lon,
      active: listing.isActive,
      serviceCategoryId: 1, // Default category
      userId: 1, // Default user
      views: listing.views,
      rating: listing.rating ?? 0.0,
      noOfRates: listing.reviewCount,
      noGuests: 4, // Default guests
      beds: 2, // Default beds
      baths: 1, // Default baths
      createdAt: listing.createdAt.toIso8601String(),
    );
  }
}
