import 'dart:io';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/feature/dashboard/data/services/properties_api_service.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'package:gather_point/feature/host/data/models/reservation_model.dart';
import 'package:gather_point/feature/host/data/models/property_type_model.dart';
import 'package:gather_point/feature/cancellation_policies/data/models/cancellation_policy_model.dart';

part 'property_creation_state.dart';

class PropertyCreationCubit extends Cubit<PropertyCreationState> {
  final PropertiesApiService _propertiesApiService;

  PropertyCreationCubit(this._propertiesApiService) : super(PropertyCreationInitial());

  /// Safe emit that checks if cubit is not closed
  void _safeEmit(PropertyCreationState state) {
    if (!isClosed) {
      emit(state);
    }
  }

  /// Load initial data (categories, facilities, property types, and cancellation policies)
  Future<void> loadInitialData() async {
    _safeEmit(PropertyCreationLoading());

    try {
      // Load all required data in parallel
      final results = await Future.wait([
        _propertiesApiService.getServiceCategories(),
        _propertiesApiService.getFacilities(),
        _propertiesApiService.getPropertyTypes(),
        _propertiesApiService.getCancellationPolicies(),
      ]);

      final categories = results[0] as List<ServiceCategory>;
      final facilities = results[1] as List<FacilityModel>;
      final propertyTypes = results[2] as List<PropertyTypeModel>;
      final cancellationPolicies = results[3] as List<CancellationPolicyModel>;

      _safeEmit(PropertyCreationDataLoaded(
        categories: categories,
        facilities: facilities,
        propertyTypes: propertyTypes,
        cancellationPolicies: cancellationPolicies,
      ));
    } catch (e) {
      _safeEmit(PropertyCreationError(e.toString()));
    }
  }

  /// Create a new property
  Future<void> createProperty({
    required Map<String, dynamic> propertyData,
    File? mainImage,
    File? video,
    List<File>? galleryImages,
  }) async {
    _safeEmit(PropertyCreationLoading());

    try {
      final property = await _propertiesApiService.createProperty(
        title: propertyData['title'],
        content: propertyData['content'],
        serviceCategoryId: propertyData['service_category_id'],
        price: propertyData['price'],
        weekendPrice: propertyData['weekend_price'],
        weekPrice: propertyData['week_price'],
        monthPrice: propertyData['month_price'],
        lat: propertyData['lat'],
        lon: propertyData['lon'],
        noGuests: propertyData['no_guests'],
        beds: propertyData['beds'],
        baths: propertyData['baths'],
        bookingRules: propertyData['booking_rules'],
        cancelationRules: propertyData['cancelation_rules'],
        propertyTypeId: propertyData['property_type_id'],
        cancellationPolicyId: propertyData['cancellation_policy_id'],
        facilityIds: propertyData['facility_ids'],
        mainImage: mainImage,
        video: video,
        galleryImages: galleryImages,
      );

      _safeEmit(PropertyCreationSuccess(property));
    } catch (e) {
      _safeEmit(PropertyCreationError(e.toString()));
    }
  }

  /// Update an existing property
  Future<void> updateProperty({
    required int propertyId,
    required Map<String, dynamic> propertyData,
    File? mainImage,
    File? video,
  }) async {
    _safeEmit(PropertyCreationLoading());

    try {
      final property = await _propertiesApiService.updateProperty(
        propertyId: propertyId,
        title: propertyData['title'],
        content: propertyData['content'],
        serviceCategoryId: propertyData['service_category_id'],
        price: propertyData['price'],
        weekendPrice: propertyData['weekend_price'],
        weekPrice: propertyData['week_price'],
        monthPrice: propertyData['month_price'],
        lat: propertyData['lat'],
        lon: propertyData['lon'],
        noGuests: propertyData['no_guests'],
        beds: propertyData['beds'],
        baths: propertyData['baths'],
        bookingRules: propertyData['booking_rules'],
        cancelationRules: propertyData['cancelation_rules'],
        propertyTypeId: propertyData['property_type_id'],
        cancellationPolicyId: propertyData['cancellation_policy_id'],
        facilityIds: propertyData['facility_ids'],
        mainImage: mainImage,
        video: video,
      );

      _safeEmit(PropertyCreationSuccess(property));
    } catch (e) {
      _safeEmit(PropertyCreationError(e.toString()));
    }
  }

  /// Upload gallery images for a property
  Future<void> uploadGalleryImages(int propertyId, List<File> images) async {
    _safeEmit(PropertyCreationLoading());

    try {
      final success = await _propertiesApiService.uploadGalleryImages(propertyId, images);

      if (success) {
        _safeEmit(PropertyGalleryUploaded());
      } else {
        _safeEmit(PropertyCreationError('Failed to upload gallery images'));
      }
    } catch (e) {
      _safeEmit(PropertyCreationError(e.toString()));
    }
  }
}
