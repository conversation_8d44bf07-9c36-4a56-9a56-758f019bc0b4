# 🔧 Host Management System - Debug Guide

## 🚨 **Common Issues & Solutions**

### **1. Backend Connection Issues**

**Problem**: API calls failing, network errors, or service unavailable
**Solutions**:

```dart
// Check if backend is running
// Verify API endpoints in lib/core/databases/api/end_points.dart

// Test with mock data first
class MockMyListingsApiService implements MyListingsApiService {
  @override
  Future<List<MyListingModel>> getMyListings() async {
    await Future.delayed(Duration(seconds: 1)); // Simulate network delay
    return [
      MyListingModel(
        id: 1,
        title: 'Test Property',
        content: 'Test Description',
        price: 100.0,
        status: 'active',
        // ... other required fields
      ),
    ];
  }
}
```

### **2. Route Errors**

**Problem**: Navigation errors, route not found
**Solutions**:

```dart
// Check route registration in lib/core/routing/routes.dart
// Verify route keys in lib/core/routing/routes_keys.dart

// Safe navigation with error handling
void navigateToCreateProperty(BuildContext context) {
  try {
    Navigator.pushNamed(context, RoutesKeys.kCreateProperty);
  } catch (e) {
    // Fallback navigation
    Navigator.pushNamed(context, '/create-property');
  }
}
```

### **3. Service Locator Issues**

**Problem**: GetIt registration errors
**Solutions**:

```dart
// Check service registration in lib/core/services/service_locator.dart
// Ensure all services are registered before use

// Debug service registration
void debugServiceLocator() {
  print('MyListingsApiService registered: ${getIt.isRegistered<MyListingsApiService>()}');
  print('PropertyEditService registered: ${getIt.isRegistered<PropertyEditService>()}');
}
```

### **4. State Management Issues**

**Problem**: BLoC state errors, cubit not emitting states
**Solutions**:

```dart
// Add debug prints to cubit
class MyListingsCubit extends Cubit<MyListingsState> {
  void loadInitialData() async {
    print('🔄 Loading initial data...');
    emit(MyListingsLoading());
    
    try {
      final listings = await _apiService.getMyListings();
      print('✅ Loaded ${listings.length} listings');
      emit(MyListingsLoaded(listings: listings, ...));
    } catch (e) {
      print('❌ Error loading listings: $e');
      emit(MyListingsError(e.toString()));
    }
  }
}
```

## 🛠️ **Quick Fixes**

### **Fix 1: Use Simplified Page**
Replace complex MyListingsPage with MyListingsPageSimple:

```dart
// In routes_branches.dart
child: const MyListingsPageSimple(), // Instead of MyListingsPage
```

### **Fix 2: Add Error Boundaries**
Wrap widgets with error handling:

```dart
Widget buildSafeWidget(Widget child) {
  return Builder(
    builder: (context) {
      try {
        return child;
      } catch (e) {
        return ErrorWidget(e);
      }
    },
  );
}
```

### **Fix 3: Mock Backend Data**
Use mock data when backend is unavailable:

```dart
class MockDataProvider {
  static List<MyListingModel> getMockListings() {
    return [
      MyListingModel(
        id: 1,
        title: 'Beautiful Apartment',
        content: 'A lovely 2-bedroom apartment',
        price: 150.0,
        status: 'active',
        isAvailable: true,
        views: 45,
        bookings: 3,
        rating: 4.5,
        reviewCount: 8,
        mainImageUrl: 'https://via.placeholder.com/300x200',
        galleryImages: ['https://via.placeholder.com/300x200'],
        category: ServiceCategory(id: 1, title: 'Apartments', icon: '', image: '', order: 1),
        noGuests: 4,
        beds: 2,
        baths: 2,
        address: 'Riyadh, Saudi Arabia',
        lat: 24.7136,
        lon: 46.6753,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        facilities: ['wifi', 'parking'],
        hasActiveReservations: false,
        pendingReservations: 0,
      ),
    ];
  }
}
```

## 🔍 **Debugging Steps**

### **Step 1: Check Service Registration**
```dart
void main() {
  setupServiceLocator();
  
  // Debug service registration
  print('Services registered:');
  print('- MyListingsApiService: ${getIt.isRegistered<MyListingsApiService>()}');
  print('- PropertyEditService: ${getIt.isRegistered<PropertyEditService>()}');
  print('- DioConsumer: ${getIt.isRegistered<DioConsumer>()}');
  
  runApp(MyApp());
}
```

### **Step 2: Test Navigation**
```dart
// Add debug navigation test
class NavigationTestPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Navigation Test')),
      body: Column(
        children: [
          ElevatedButton(
            onPressed: () => _testRoute(context, RoutesKeys.kCreateProperty),
            child: Text('Test Create Property'),
          ),
          ElevatedButton(
            onPressed: () => _testRoute(context, RoutesKeys.kEditProperty),
            child: Text('Test Edit Property'),
          ),
        ],
      ),
    );
  }
  
  void _testRoute(BuildContext context, String route) {
    try {
      Navigator.pushNamed(context, route);
      print('✅ Navigation to $route successful');
    } catch (e) {
      print('❌ Navigation to $route failed: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Navigation failed: $e')),
      );
    }
  }
}
```

### **Step 3: Test API Calls**
```dart
// Add API test page
class ApiTestPage extends StatefulWidget {
  @override
  _ApiTestPageState createState() => _ApiTestPageState();
}

class _ApiTestPageState extends State<ApiTestPage> {
  String _result = 'No test run yet';
  
  void _testApiCall() async {
    setState(() => _result = 'Testing...');
    
    try {
      final service = getIt<MyListingsApiService>();
      final listings = await service.getMyListings();
      setState(() => _result = 'Success: ${listings.length} listings');
    } catch (e) {
      setState(() => _result = 'Error: $e');
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('API Test')),
      body: Column(
        children: [
          ElevatedButton(
            onPressed: _testApiCall,
            child: Text('Test API Call'),
          ),
          Text(_result),
        ],
      ),
    );
  }
}
```

## 🚀 **Emergency Fallback**

If everything fails, use this minimal working version:

```dart
class EmergencyMyListingsPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('My Listings'),
        actions: [
          IconButton(
            onPressed: () => Navigator.pushNamed(context, '/create-property'),
            icon: Icon(Icons.add),
          ),
        ],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.home_work, size: 64),
            SizedBox(height: 16),
            Text('My Listings'),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.pushNamed(context, '/create-property'),
              child: Text('Create New Listing'),
            ),
          ],
        ),
      ),
    );
  }
}
```

## 📱 **Testing Checklist**

- [ ] Service locator setup complete
- [ ] All routes registered
- [ ] Backend API accessible
- [ ] BLoC states working
- [ ] Navigation working
- [ ] Error handling in place
- [ ] Fallback UI available
- [ ] Localization working

## 🔧 **Quick Commands**

```bash
# Clean and rebuild
flutter clean && flutter pub get && flutter build apk --debug

# Check for issues
flutter analyze

# Run with verbose logging
flutter run --verbose

# Check dependencies
flutter pub deps
```
