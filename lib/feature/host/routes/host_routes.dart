import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/databases/api/api_consumer.dart';
import 'package:gather_point/feature/host/presentation/views/my_listings_page.dart';
import 'package:gather_point/feature/host/presentation/views/edit_property_page.dart';
import 'package:gather_point/feature/host/presentation/views/listing_analytics_page.dart';
import 'package:gather_point/feature/host/presentation/cubit/my_listings_cubit.dart';
import 'package:gather_point/feature/host/presentation/cubit/property_creation_cubit.dart';
import 'package:gather_point/feature/host/data/services/my_listings_api_service.dart';
import 'package:gather_point/feature/host/services/property_edit_service.dart';
import 'package:gather_point/feature/dashboard/data/services/properties_api_service.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';

class HostRoutes {
  static const String myListings = '/host/my-listings';
  static const String editProperty = '/host/edit-property';
  static const String propertyAnalytics = '/host/property-analytics';
  static const String createProperty = '/host/create-property';
  static const String propertyDetails = '/host/property-details';

  static Map<String, WidgetBuilder> getRoutes() {
    return {
      myListings: (context) => BlocProvider(
        create: (context) => MyListingsCubit(
          MyListingsApiService(context.read<ApiConsumer>()),
        ),
        child: const MyListingsPage(),
      ),
      editProperty: (context) => _buildEditPropertyPage(context),
      propertyAnalytics: (context) => _buildAnalyticsPage(context),
    };
  }

  static Widget _buildEditPropertyPage(BuildContext context) {
    final args = ModalRoute.of(context)?.settings.arguments;
    
    if (args is Map<String, dynamic>) {
      final propertyId = args['propertyId'] as int?;
      final propertyData = args['propertyData'] as MyListingModel?;
      
      if (propertyId != null) {
        return MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (context) => PropertyCreationCubit(
                context.read<PropertiesApiService>(),
                PropertyEditService(context.read<ApiConsumer>()),
              ),
            ),
          ],
          child: EditPropertyPage(
            propertyId: propertyId,
            initialData: propertyData,
          ),
        );
      }
    }
    
    // Fallback for invalid arguments
    return Scaffold(
      appBar: AppBar(title: const Text('خطأ')),
      body: const Center(
        child: Text('معاملات غير صحيحة'),
      ),
    );
  }

  static Widget _buildAnalyticsPage(BuildContext context) {
    final args = ModalRoute.of(context)?.settings.arguments;
    
    if (args is Map<String, dynamic>) {
      final propertyId = args['propertyId'] as int?;
      final propertyTitle = args['propertyTitle'] as String?;
      
      if (propertyId != null) {
        return ListingAnalyticsPage(
          listingId: propertyId,
          listingTitle: propertyTitle,
        );
      }
    }
    
    // Fallback for invalid arguments
    return Scaffold(
      appBar: AppBar(title: const Text('خطأ')),
      body: const Center(
        child: Text('معاملات غير صحيحة'),
      ),
    );
  }

  // Navigation helper methods
  static Future<void> navigateToMyListings(BuildContext context) {
    return Navigator.pushNamed(context, myListings);
  }

  static Future<void> navigateToEditProperty(
    BuildContext context, {
    required int propertyId,
    MyListingModel? propertyData,
  }) {
    return Navigator.pushNamed(
      context,
      editProperty,
      arguments: {
        'propertyId': propertyId,
        'propertyData': propertyData,
      },
    );
  }

  static Future<void> navigateToPropertyAnalytics(
    BuildContext context, {
    required int propertyId,
    String? propertyTitle,
  }) {
    return Navigator.pushNamed(
      context,
      propertyAnalytics,
      arguments: {
        'propertyId': propertyId,
        'propertyTitle': propertyTitle,
      },
    );
  }

  static Future<bool?> navigateToEditPropertyAndWaitForResult(
    BuildContext context, {
    required int propertyId,
    MyListingModel? propertyData,
  }) {
    return Navigator.pushNamed(
      context,
      editProperty,
      arguments: {
        'propertyId': propertyId,
        'propertyData': propertyData,
      },
    ).then((result) => result as bool?);
  }

  // Deep link handlers
  static Route<dynamic>? handleDeepLink(RouteSettings settings) {
    final uri = Uri.parse(settings.name ?? '');
    
    // Handle /host/property/{id}/edit
    if (uri.pathSegments.length >= 4 &&
        uri.pathSegments[0] == 'host' &&
        uri.pathSegments[1] == 'property' &&
        uri.pathSegments[3] == 'edit') {
      final propertyId = int.tryParse(uri.pathSegments[2]);
      if (propertyId != null) {
        return MaterialPageRoute(
          builder: (context) => _buildEditPropertyPage(context),
          settings: RouteSettings(
            name: editProperty,
            arguments: {'propertyId': propertyId},
          ),
        );
      }
    }
    
    // Handle /host/property/{id}/analytics
    if (uri.pathSegments.length >= 4 &&
        uri.pathSegments[0] == 'host' &&
        uri.pathSegments[1] == 'property' &&
        uri.pathSegments[3] == 'analytics') {
      final propertyId = int.tryParse(uri.pathSegments[2]);
      if (propertyId != null) {
        return MaterialPageRoute(
          builder: (context) => _buildAnalyticsPage(context),
          settings: RouteSettings(
            name: propertyAnalytics,
            arguments: {'propertyId': propertyId},
          ),
        );
      }
    }
    
    return null;
  }

  // Route guards
  static bool canAccessHostRoutes(BuildContext context) {
    // Add your authentication/authorization logic here
    // For example, check if user is logged in and has host permissions
    return true; // Placeholder
  }

  static Widget buildRouteGuard(
    BuildContext context,
    Widget child,
    String routeName,
  ) {
    if (!canAccessHostRoutes(context)) {
      return Scaffold(
        appBar: AppBar(title: const Text('غير مصرح')),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.lock, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text('يجب تسجيل الدخول للوصول إلى هذه الصفحة'),
            ],
          ),
        ),
      );
    }
    
    return child;
  }

  // Route transitions
  static Route<T> createSlideRoute<T extends Object?>(
    Widget page,
    RouteSettings settings,
  ) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.ease;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
    );
  }

  static Route<T> createFadeRoute<T extends Object?>(
    Widget page,
    RouteSettings settings,
  ) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: child,
        );
      },
    );
  }
}
