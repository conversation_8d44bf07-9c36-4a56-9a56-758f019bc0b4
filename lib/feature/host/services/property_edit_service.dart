import 'package:gather_point/core/databases/api/api_consumer.dart';
import 'package:gather_point/core/databases/api/end_points.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';

class PropertyEditService {
  final ApiConsumer _apiConsumer;

  PropertyEditService(this._apiConsumer);

  /// Get property data for editing
  Future<MyListingModel> getPropertyForEdit(int propertyId) async {
    try {
      final response = await _apiConsumer.get(
        '${EndPoints.hostListings}/$propertyId/edit',
      );

      if (response['success'] == true && response['data'] != null) {
        return MyListingModel.fromJson(response['data']);
      }

      throw Exception('Property not found');
    } catch (e) {
      throw Exception('Failed to load property data: $e');
    }
  }

  /// Update property data
  Future<MyListingModel> updateProperty(
    int propertyId,
    Map<String, dynamic> propertyData,
  ) async {
    try {
      final response = await _apiConsumer.put(
        '${EndPoints.hostListings}/$propertyId',
        data: propertyData,
      );

      if (response['success'] == true && response['data'] != null) {
        return MyListingModel.fromJson(response['data']);
      }

      throw Exception('Failed to update property');
    } catch (e) {
      throw Exception('Failed to update property: $e');
    }
  }

  /// Update property images
  Future<List<String>> updatePropertyImages(
    int propertyId,
    List<String> imageUrls,
  ) async {
    try {
      final response = await _apiConsumer.put(
        '${EndPoints.hostListings}/$propertyId/images',
        data: {
          'images': imageUrls,
        },
      );

      if (response['success'] == true && response['data'] != null) {
        return List<String>.from(response['data']['images'] ?? []);
      }

      throw Exception('Failed to update images');
    } catch (e) {
      throw Exception('Failed to update property images: $e');
    }
  }

  /// Update property location
  Future<void> updatePropertyLocation(
    int propertyId,
    double latitude,
    double longitude,
    String address,
  ) async {
    try {
      final response = await _apiConsumer.put(
        '${EndPoints.hostListings}/$propertyId/location',
        data: {
          'latitude': latitude,
          'longitude': longitude,
          'address': address,
        },
      );

      if (response['success'] != true) {
        throw Exception('Failed to update location');
      }
    } catch (e) {
      throw Exception('Failed to update property location: $e');
    }
  }

  /// Update property pricing
  Future<void> updatePropertyPricing(
    int propertyId,
    double price,
    Map<String, dynamic>? pricingRules,
  ) async {
    try {
      final response = await _apiConsumer.put(
        '${EndPoints.hostListings}/$propertyId/pricing',
        data: {
          'price': price,
          'pricing_rules': pricingRules ?? {},
        },
      );

      if (response['success'] != true) {
        throw Exception('Failed to update pricing');
      }
    } catch (e) {
      throw Exception('Failed to update property pricing: $e');
    }
  }

  /// Update property amenities
  Future<void> updatePropertyAmenities(
    int propertyId,
    List<int> amenityIds,
  ) async {
    try {
      final response = await _apiConsumer.put(
        '${EndPoints.hostListings}/$propertyId/amenities',
        data: {
          'amenity_ids': amenityIds,
        },
      );

      if (response['success'] != true) {
        throw Exception('Failed to update amenities');
      }
    } catch (e) {
      throw Exception('Failed to update property amenities: $e');
    }
  }

  /// Update property availability
  Future<void> updatePropertyAvailability(
    int propertyId,
    Map<String, dynamic> availabilityData,
  ) async {
    try {
      final response = await _apiConsumer.put(
        '${EndPoints.hostListings}/$propertyId/availability',
        data: availabilityData,
      );

      if (response['success'] != true) {
        throw Exception('Failed to update availability');
      }
    } catch (e) {
      throw Exception('Failed to update property availability: $e');
    }
  }

  /// Get property edit history
  Future<List<Map<String, dynamic>>> getPropertyEditHistory(
    int propertyId, {
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiConsumer.get(
        '${EndPoints.hostListings}/$propertyId/edit-history',
        queryParameters: {
          'page': page,
          'limit': limit,
        },
      );

      if (response['success'] == true && response['data'] != null) {
        final List<dynamic> historyData = response['data']['history'] ?? [];
        return historyData.cast<Map<String, dynamic>>();
      }

      return [];
    } catch (e) {
      throw Exception('Failed to get edit history: $e');
    }
  }

  /// Save property as draft
  Future<void> savePropertyAsDraft(
    int propertyId,
    Map<String, dynamic> propertyData,
  ) async {
    try {
      final response = await _apiConsumer.put(
        '${EndPoints.hostListings}/$propertyId/draft',
        data: propertyData,
      );

      if (response['success'] != true) {
        throw Exception('Failed to save draft');
      }
    } catch (e) {
      throw Exception('Failed to save property as draft: $e');
    }
  }

  /// Validate property data before update
  Future<Map<String, dynamic>> validatePropertyData(
    int propertyId,
    Map<String, dynamic> propertyData,
  ) async {
    try {
      final response = await _apiConsumer.post(
        '${EndPoints.hostListings}/$propertyId/validate',
        data: propertyData,
      );

      if (response['success'] == true) {
        return response['data'] ?? {};
      }

      return {
        'valid': false,
        'errors': ['Validation failed'],
      };
    } catch (e) {
      return {
        'valid': false,
        'errors': ['Validation error: $e'],
      };
    }
  }

  /// Generate mock property data for development
  static MyListingModel generateMockPropertyData(int propertyId) {
    return MyListingModel(
      id: propertyId,
      title: 'فيلا عصرية في الرياض - للتعديل',
      content: 'فيلا واسعة 5 غرف نوم مع حديقة ومسبح خاص. تم تجهيزها بأحدث المرافق والتقنيات.',
      price: 1200.0,
      status: 'active',
      isAvailable: true,
      views: 245,
      bookings: 12,
      rating: 4.8,
      reviewCount: 8,
      mainImageUrl: 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800',
      galleryImages: [
        'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800',
        'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800',
        'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800',
      ],
      category: ServiceCategory(
        id: 1,
        title: 'فلل',
        icon: 'villa',
        image: '',
        order: 1,
      ),
      noGuests: 8,
      beds: 5,
      baths: 4,
      address: 'الرياض، حي النخيل',
      lat: 24.7136,
      lon: 46.6753,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      facilities: ['wifi', 'parking', 'pool', 'garden'],
      hasActiveReservations: true,
      pendingReservations: 2,
    );
  }

  /// Compare property data to detect changes
  static Map<String, dynamic> detectChanges(
    MyListingModel original,
    Map<String, dynamic> updated,
  ) {
    final changes = <String, dynamic>{};

    // Compare basic fields
    if (updated['title'] != original.title) {
      changes['title'] = {
        'old': original.title,
        'new': updated['title'],
      };
    }

    if (updated['content'] != original.content) {
      changes['content'] = {
        'old': original.content,
        'new': updated['content'],
      };
    }

    if (updated['price'] != original.price) {
      changes['price'] = {
        'old': original.price,
        'new': updated['price'],
      };
    }

    if (updated['address'] != original.address) {
      changes['address'] = {
        'old': original.address,
        'new': updated['address'],
      };
    }

    // Compare images
    final originalImages = original.galleryImages;
    final updatedImages = List<String>.from(updated['images'] ?? []);
    
    if (!_listsEqual(originalImages, updatedImages)) {
      changes['images'] = {
        'old': originalImages,
        'new': updatedImages,
      };
    }

    return changes;
  }

  static bool _listsEqual<T>(List<T> list1, List<T> list2) {
    if (list1.length != list2.length) return false;
    for (int i = 0; i < list1.length; i++) {
      if (list1[i] != list2[i]) return false;
    }
    return true;
  }

  /// Get property change summary
  static String getChangeSummary(Map<String, dynamic> changes) {
    if (changes.isEmpty) return 'لا توجد تغييرات';

    final changeCount = changes.length;
    final changedFields = changes.keys.toList();

    if (changeCount == 1) {
      return 'تم تغيير ${_getFieldDisplayName(changedFields.first)}';
    } else if (changeCount <= 3) {
      final fieldNames = changedFields.map(_getFieldDisplayName).join('، ');
      return 'تم تغيير $fieldNames';
    } else {
      return 'تم تغيير $changeCount حقول';
    }
  }

  static String _getFieldDisplayName(String fieldName) {
    switch (fieldName) {
      case 'title':
        return 'العنوان';
      case 'content':
        return 'الوصف';
      case 'price':
        return 'السعر';
      case 'location':
        return 'الموقع';
      case 'images':
        return 'الصور';
      default:
        return fieldName;
    }
  }
}
