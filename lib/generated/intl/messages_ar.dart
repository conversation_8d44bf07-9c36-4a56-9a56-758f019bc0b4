// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  static String m0(price) => "${price} ر.س";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "about": MessageLookupByLibrary.simpleMessage("حول التطبيق"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("حول التطبيق"),
        "aboutThisPlace":
            MessageLookupByLibrary.simpleMessage("حول هذا المكان"),
        "accessibility": MessageLookupByLibrary.simpleMessage("إمكانية الوصول"),
        "accountSettings":
            MessageLookupByLibrary.simpleMessage("إعدادات الحساب"),
        "actionParameters":
            MessageLookupByLibrary.simpleMessage("معاملات الإجراء"),
        "activate": MessageLookupByLibrary.simpleMessage("تفعيل"),
        "activateAll": MessageLookupByLibrary.simpleMessage("تفعيل الكل"),
        "activateAllDescription": MessageLookupByLibrary.simpleMessage(
            "جعل جميع العقارات المحددة مرئية للضيوف"),
        "activateSelected":
            MessageLookupByLibrary.simpleMessage("تفعيل المحدد"),
        "activateSelectedConfirmation": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد من تفعيل العقارات المحددة؟"),
        "active": MessageLookupByLibrary.simpleMessage("نشطة"),
        "activeListings":
            MessageLookupByLibrary.simpleMessage("العقارات النشطة"),
        "activeStatusDescription":
            MessageLookupByLibrary.simpleMessage("عقارك مُفعل ومرئي للضيوف"),
        "addImage": MessageLookupByLibrary.simpleMessage("إضافة صورة"),
        "addImages": MessageLookupByLibrary.simpleMessage("إضافة صور"),
        "addNewListing":
            MessageLookupByLibrary.simpleMessage("إضافة إعلان جديد"),
        "addProperty": MessageLookupByLibrary.simpleMessage("إضافة عقار"),
        "addToFavorites":
            MessageLookupByLibrary.simpleMessage("أضف إلى المفضلة"),
        "additionalInfo":
            MessageLookupByLibrary.simpleMessage("معلومات إضافية"),
        "additionalSettings":
            MessageLookupByLibrary.simpleMessage("إعدادات إضافية"),
        "address": MessageLookupByLibrary.simpleMessage("العنوان"),
        "advancedBulkActions":
            MessageLookupByLibrary.simpleMessage("إجراءات جماعية متقدمة"),
        "airConditioning": MessageLookupByLibrary.simpleMessage("تكييف"),
        "all": MessageLookupByLibrary.simpleMessage("الكل"),
        "allCategories": MessageLookupByLibrary.simpleMessage("جميع الفئات"),
        "allListings": MessageLookupByLibrary.simpleMessage("جميع العقارات"),
        "allReviews": MessageLookupByLibrary.simpleMessage("الكل"),
        "amenities": MessageLookupByLibrary.simpleMessage("المرافق"),
        "analytics": MessageLookupByLibrary.simpleMessage("التحليلات"),
        "analyticsOverview":
            MessageLookupByLibrary.simpleMessage("نظرة عامة على التحليلات"),
        "appDescription": MessageLookupByLibrary.simpleMessage(
            "تطبيق لحجز العقارات والأماكن"),
        "appInfo":
            MessageLookupByLibrary.simpleMessage("معلومات التطبيق والإصدار"),
        "appInformation":
            MessageLookupByLibrary.simpleMessage("معلومات التطبيق والإصدار"),
        "appName": MessageLookupByLibrary.simpleMessage("نقطة تجمع"),
        "appearance": MessageLookupByLibrary.simpleMessage("المظهر"),
        "applyAction": MessageLookupByLibrary.simpleMessage("تطبيق الإجراء"),
        "applyDiscount": MessageLookupByLibrary.simpleMessage("تطبيق خصم"),
        "applyDiscountDescription":
            MessageLookupByLibrary.simpleMessage("تطبيق خصم مؤقت"),
        "applyFilter": MessageLookupByLibrary.simpleMessage("تطبيق التصفية"),
        "applyFilters": MessageLookupByLibrary.simpleMessage("تطبيق"),
        "arabic": MessageLookupByLibrary.simpleMessage("العربية"),
        "archiveAll": MessageLookupByLibrary.simpleMessage("أرشفة الكل"),
        "archiveAllDescription":
            MessageLookupByLibrary.simpleMessage("أرشفة العقارات المحددة"),
        "availableBalance":
            MessageLookupByLibrary.simpleMessage("الرصيد المتاح"),
        "availableServices":
            MessageLookupByLibrary.simpleMessage("الخدمات المتوفرة"),
        "average": MessageLookupByLibrary.simpleMessage("متوسط"),
        "averageDailyRate":
            MessageLookupByLibrary.simpleMessage("متوسط السعر اليومي"),
        "averageRating": MessageLookupByLibrary.simpleMessage("متوسط التقييم"),
        "back": MessageLookupByLibrary.simpleMessage("رجوع"),
        "backToSearch": MessageLookupByLibrary.simpleMessage("العودة للبحث"),
        "balcony": MessageLookupByLibrary.simpleMessage("شرفة"),
        "bankTransfer": MessageLookupByLibrary.simpleMessage("تحويل بنكي"),
        "bar": MessageLookupByLibrary.simpleMessage("بار"),
        "basicInformation":
            MessageLookupByLibrary.simpleMessage("المعلومات الأساسية"),
        "bathrooms": MessageLookupByLibrary.simpleMessage("حمامات"),
        "bathsInvalid": MessageLookupByLibrary.simpleMessage(
            "عدد الحمامات يجب أن يكون بين 1 و 10"),
        "bathsRequired":
            MessageLookupByLibrary.simpleMessage("عدد الحمامات مطلوب"),
        "beachAccess": MessageLookupByLibrary.simpleMessage("وصول للشاطئ"),
        "bedrooms": MessageLookupByLibrary.simpleMessage("غرف النوم"),
        "bedsInvalid": MessageLookupByLibrary.simpleMessage(
            "عدد غرف النوم يجب أن يكون بين 1 و 10"),
        "bedsRequired":
            MessageLookupByLibrary.simpleMessage("عدد غرف النوم مطلوب"),
        "bio": MessageLookupByLibrary.simpleMessage("الوصف"),
        "birthdate": MessageLookupByLibrary.simpleMessage("تاريخ الميلاد"),
        "bookNow": MessageLookupByLibrary.simpleMessage("احجز الآن"),
        "bookingDate": MessageLookupByLibrary.simpleMessage("تاريخ الحجز"),
        "bookingDetails":
            MessageLookupByLibrary.simpleMessage("تفاصيل وسياسات الحجز"),
        "bookingFee": MessageLookupByLibrary.simpleMessage("رسوم الحجز"),
        "bookingMetrics": MessageLookupByLibrary.simpleMessage("مقاييس الحجز"),
        "bookingPolicy": MessageLookupByLibrary.simpleMessage("سياسة الحجز"),
        "bookingRules": MessageLookupByLibrary.simpleMessage("قواعد الحجز"),
        "bookingStatus": MessageLookupByLibrary.simpleMessage("حالة الحجز"),
        "bookingSummary": MessageLookupByLibrary.simpleMessage("ملخص الحجز"),
        "bookingsChart": MessageLookupByLibrary.simpleMessage("مخطط الحجوزات"),
        "bookingsOverview":
            MessageLookupByLibrary.simpleMessage("نظرة عامة على الحجوزات"),
        "browseReels": MessageLookupByLibrary.simpleMessage("تصفح الريلز"),
        "budgetFriendly":
            MessageLookupByLibrary.simpleMessage("صديق للميزانية"),
        "bulkActionCompleted": MessageLookupByLibrary.simpleMessage(
            "تم تنفيذ الإجراء الجماعي بنجاح"),
        "bulkActionError": MessageLookupByLibrary.simpleMessage(
            "خطأ في تنفيذ الإجراء الجماعي"),
        "bulkActions": MessageLookupByLibrary.simpleMessage("إجراءات جماعية"),
        "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
        "cancelBooking": MessageLookupByLibrary.simpleMessage("إلغاء الحجز"),
        "cancelReservation":
            MessageLookupByLibrary.simpleMessage("إلغاء الحجز"),
        "cancelSelection":
            MessageLookupByLibrary.simpleMessage("إلغاء التحديد"),
        "cancellationPolicy":
            MessageLookupByLibrary.simpleMessage("سياسة الإلغاء"),
        "cancellationPolicyNote": MessageLookupByLibrary.simpleMessage(
            "تذكر أن السياسة التي يضعها المضيف تناسب ظروفك، في حالات نادرة قد تكون مؤهلاً لاسترداد جزئي أو كامل وفقاً لسياسة الموقع."),
        "cancellationPolicyRequired":
            MessageLookupByLibrary.simpleMessage("يرجى اختيار سياسة الإلغاء"),
        "cancellationRate":
            MessageLookupByLibrary.simpleMessage("معدل الإلغاء"),
        "cancellationRules":
            MessageLookupByLibrary.simpleMessage("قواعد الإلغاء"),
        "cancelled": MessageLookupByLibrary.simpleMessage("ملغية"),
        "cancelledBookings":
            MessageLookupByLibrary.simpleMessage("الحجوزات الملغية"),
        "categories": MessageLookupByLibrary.simpleMessage("الفئات"),
        "category": MessageLookupByLibrary.simpleMessage("الفئة"),
        "categoryAndType": MessageLookupByLibrary.simpleMessage("الفئة والنوع"),
        "categoryRequired":
            MessageLookupByLibrary.simpleMessage("يرجى اختيار فئة"),
        "change": MessageLookupByLibrary.simpleMessage("تغيير"),
        "changeListingStatus":
            MessageLookupByLibrary.simpleMessage("تغيير حالة العقار"),
        "changeReason": MessageLookupByLibrary.simpleMessage("سبب التغيير"),
        "changeStatus": MessageLookupByLibrary.simpleMessage("تغيير الحالة"),
        "charts": MessageLookupByLibrary.simpleMessage("الرسوم البيانية"),
        "checkConnection":
            MessageLookupByLibrary.simpleMessage("تحقق من اتصالك بالإنترنت"),
        "checkIn": MessageLookupByLibrary.simpleMessage("تسجيل الوصول"),
        "checkInDate":
            MessageLookupByLibrary.simpleMessage("تاريخ تسجيل الوصول"),
        "checkInInstructions":
            MessageLookupByLibrary.simpleMessage("تعليمات تسجيل الوصول"),
        "checkInOut": MessageLookupByLibrary.simpleMessage("الوصول/المغادرة"),
        "checkOut": MessageLookupByLibrary.simpleMessage("تسجيل المغادرة"),
        "checkOutDate":
            MessageLookupByLibrary.simpleMessage("تاريخ تسجيل المغادرة"),
        "cityView": MessageLookupByLibrary.simpleMessage("إطلالة المدينة"),
        "clearFilter": MessageLookupByLibrary.simpleMessage("مسح التصفية"),
        "clearFilters": MessageLookupByLibrary.simpleMessage("مسح الفلاتر"),
        "clearSelection": MessageLookupByLibrary.simpleMessage("إلغاء التحديد"),
        "codeExpired":
            MessageLookupByLibrary.simpleMessage("انتهت صلاحية الكود"),
        "comment": MessageLookupByLibrary.simpleMessage("تعليق"),
        "commentFailed":
            MessageLookupByLibrary.simpleMessage("فشل في نشر التعليق"),
        "commentPosted":
            MessageLookupByLibrary.simpleMessage("تم نشر التعليق بنجاح"),
        "comments": MessageLookupByLibrary.simpleMessage("التعليقات"),
        "commission": MessageLookupByLibrary.simpleMessage("عمولة"),
        "completed": MessageLookupByLibrary.simpleMessage("مكتملة"),
        "confirm": MessageLookupByLibrary.simpleMessage("تأكيد"),
        "confirmBooking": MessageLookupByLibrary.simpleMessage("تأكيد الحجز"),
        "confirmLocation": MessageLookupByLibrary.simpleMessage("تأكيد الموقع"),
        "confirmReservation":
            MessageLookupByLibrary.simpleMessage("تأكيد الحجز"),
        "confirmSubmission":
            MessageLookupByLibrary.simpleMessage("تأكيد الإرسال"),
        "confirmSubmissionMessage": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد من إرسال هذا العقار؟"),
        "confirmed": MessageLookupByLibrary.simpleMessage("مؤكدة"),
        "confirmedBookings":
            MessageLookupByLibrary.simpleMessage("الحجوزات المؤكدة"),
        "contactHost": MessageLookupByLibrary.simpleMessage("تواصل مع المضيف"),
        "contactInfo": MessageLookupByLibrary.simpleMessage("معلومات التواصل"),
        "contactSupport":
            MessageLookupByLibrary.simpleMessage("تواصل مع الدعم"),
        "contactUs": MessageLookupByLibrary.simpleMessage("اتصل بنا"),
        "contactUsDesc":
            MessageLookupByLibrary.simpleMessage("تواصل مع فريق الدعم لدينا"),
        "continueButton": MessageLookupByLibrary.simpleMessage("المتابعة"),
        "conversionRate": MessageLookupByLibrary.simpleMessage("معدل التحويل"),
        "convertToDraft":
            MessageLookupByLibrary.simpleMessage("تحويل إلى مسودة"),
        "convertToDraftDescription": MessageLookupByLibrary.simpleMessage(
            "تحويل جميع العقارات المحددة إلى حالة مسودة"),
        "createFirstListing":
            MessageLookupByLibrary.simpleMessage("إنشاء أول عقار"),
        "createNewProperty":
            MessageLookupByLibrary.simpleMessage("إنشاء عقار جديد"),
        "createProperty": MessageLookupByLibrary.simpleMessage("إنشاء عقار"),
        "currencyCode": MessageLookupByLibrary.simpleMessage("ر.س"),
        "currencySymbol": MessageLookupByLibrary.simpleMessage("ر.س"),
        "currentLocation":
            MessageLookupByLibrary.simpleMessage("الموقع الحالي"),
        "currentStatus": MessageLookupByLibrary.simpleMessage("الحالة الحالية"),
        "currentStatusBreakdown":
            MessageLookupByLibrary.simpleMessage("تفصيل الحالة الحالية"),
        "customizeExperience":
            MessageLookupByLibrary.simpleMessage("خصص تجربتك في التطبيق"),
        "dailyBookings":
            MessageLookupByLibrary.simpleMessage("الحجوزات اليومية"),
        "dailyPrice": MessageLookupByLibrary.simpleMessage("السعر اليومي"),
        "dailyRevenue":
            MessageLookupByLibrary.simpleMessage("الإيرادات اليومية"),
        "dailyViews": MessageLookupByLibrary.simpleMessage("المشاهدات اليومية"),
        "darkMode": MessageLookupByLibrary.simpleMessage("الوضع المظلم"),
        "dashboardOverview": MessageLookupByLibrary.simpleMessage("نظرة عامة"),
        "dataAndPrivacy":
            MessageLookupByLibrary.simpleMessage("البيانات والخصوصية"),
        "dataCollection": MessageLookupByLibrary.simpleMessage("جمع البيانات"),
        "dataCollectionDesc":
            MessageLookupByLibrary.simpleMessage("كيف نجمع ونستخدم بياناتك"),
        "dataLoadError":
            MessageLookupByLibrary.simpleMessage("حدث خطأ في تحميل البيانات"),
        "dataRetention":
            MessageLookupByLibrary.simpleMessage("الاحتفاظ بالبيانات"),
        "dataRetentionDesc":
            MessageLookupByLibrary.simpleMessage("كم من الوقت نحتفظ ببياناتك"),
        "dates": MessageLookupByLibrary.simpleMessage("التواريخ"),
        "days": MessageLookupByLibrary.simpleMessage("أيام"),
        "deactivate": MessageLookupByLibrary.simpleMessage("إلغاء تفعيل"),
        "deactivateAll":
            MessageLookupByLibrary.simpleMessage("إلغاء تفعيل الكل"),
        "deactivateAllDescription": MessageLookupByLibrary.simpleMessage(
            "إخفاء جميع العقارات المحددة عن الضيوف"),
        "deactivateListing":
            MessageLookupByLibrary.simpleMessage("إلغاء تفعيل العقار"),
        "deactivateListingConfirmation": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد من إلغاء تفعيل هذا العقار؟"),
        "deactivateSelected":
            MessageLookupByLibrary.simpleMessage("إلغاء تفعيل المحدد"),
        "deactivateSelectedConfirmation": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد من إلغاء تفعيل العقارات المحددة؟"),
        "decreasePrices": MessageLookupByLibrary.simpleMessage("تقليل الأسعار"),
        "decreasePricesDescription":
            MessageLookupByLibrary.simpleMessage("تقليل الأسعار بنسبة مئوية"),
        "deleteAccount": MessageLookupByLibrary.simpleMessage("حذف الحساب"),
        "deleteAccountDesc":
            MessageLookupByLibrary.simpleMessage("حذف حسابك وبياناتك نهائياً"),
        "deleteAll": MessageLookupByLibrary.simpleMessage("حذف الكل"),
        "deleteAllDescription": MessageLookupByLibrary.simpleMessage(
            "حذف جميع العقارات المحددة نهائياً"),
        "deleteComment": MessageLookupByLibrary.simpleMessage("حذف التعليق"),
        "deleteSelected": MessageLookupByLibrary.simpleMessage("حذف المحدد"),
        "deleteSelectedConfirmation": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد من حذف العقارات المحددة؟ لا يمكن التراجع عن هذا الإجراء."),
        "description": MessageLookupByLibrary.simpleMessage("الوصف"),
        "details": MessageLookupByLibrary.simpleMessage("التفاصيل"),
        "detectingLocation":
            MessageLookupByLibrary.simpleMessage("جارٍ تحديد المدينة..."),
        "didntReceiveCode":
            MessageLookupByLibrary.simpleMessage("لم تستلم الكود؟"),
        "discountDuration": MessageLookupByLibrary.simpleMessage("مدة الخصم"),
        "discountPercentage":
            MessageLookupByLibrary.simpleMessage("نسبة الخصم"),
        "discoverLatestVisualContent":
            MessageLookupByLibrary.simpleMessage("اكتشف أحدث المحتوى المرئي"),
        "discoverMore": MessageLookupByLibrary.simpleMessage("اكتشف المزيد"),
        "downloadReceipt":
            MessageLookupByLibrary.simpleMessage("تحميل الإيصال"),
        "draftStatusDescription": MessageLookupByLibrary.simpleMessage(
            "عقارك محفوظ ولكن غير منشور بعد"),
        "drafts": MessageLookupByLibrary.simpleMessage("المسودات"),
        "duplicateAll": MessageLookupByLibrary.simpleMessage("نسخ الكل"),
        "duplicateAllDescription": MessageLookupByLibrary.simpleMessage(
            "إنشاء نسخ من العقارات المحددة"),
        "earnings": MessageLookupByLibrary.simpleMessage("الأرباح"),
        "earningsChart": MessageLookupByLibrary.simpleMessage("مخطط الأرباح"),
        "earningsOverview":
            MessageLookupByLibrary.simpleMessage("نظرة عامة على الأرباح"),
        "editComment": MessageLookupByLibrary.simpleMessage("تعديل التعليق"),
        "editListing": MessageLookupByLibrary.simpleMessage("تعديل العقار"),
        "editProfile":
            MessageLookupByLibrary.simpleMessage("تعديل الملف الشخصي"),
        "editProperty": MessageLookupByLibrary.simpleMessage("تعديل العقار"),
        "editWhilePending":
            MessageLookupByLibrary.simpleMessage("تعديل أثناء المراجعة"),
        "email": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
        "enableAllNotifications": MessageLookupByLibrary.simpleMessage(
            "تفعيل أو إلغاء جميع الإشعارات"),
        "enableHostMode": MessageLookupByLibrary.simpleMessage(
            "قم بتفعيل وضع المستضيف لإدارة عقاراتك"),
        "enableNotificationsInSettings": MessageLookupByLibrary.simpleMessage(
            "يرجى تفعيل الإشعارات في إعدادات الجهاز"),
        "engagementMetrics":
            MessageLookupByLibrary.simpleMessage("مقاييس التفاعل"),
        "english": MessageLookupByLibrary.simpleMessage("الإنجليزية"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("أدخل المبلغ"),
        "enterBookingRules":
            MessageLookupByLibrary.simpleMessage("أدخل قواعد الحجز"),
        "enterCancellationRules":
            MessageLookupByLibrary.simpleMessage("أدخل قواعد الإلغاء"),
        "enterChangeReason":
            MessageLookupByLibrary.simpleMessage("أدخل سبب تغيير الحالة..."),
        "enterPhoneNumber":
            MessageLookupByLibrary.simpleMessage("ادخل رقم الجوال"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("أدخل السعر"),
        "enterPropertyDescription":
            MessageLookupByLibrary.simpleMessage("أدخل وصف العقار"),
        "enterPropertyTitle":
            MessageLookupByLibrary.simpleMessage("أدخل عنوان العقار"),
        "enterSearchTerm":
            MessageLookupByLibrary.simpleMessage("أدخل كلمة البحث..."),
        "enterVerificationCode":
            MessageLookupByLibrary.simpleMessage("أدخل كود التحقق"),
        "error": MessageLookupByLibrary.simpleMessage("خطأ"),
        "errorFetchingInfo":
            MessageLookupByLibrary.simpleMessage("حدث خطأ أثناء جلب المعلومات"),
        "errorLoadingListings":
            MessageLookupByLibrary.simpleMessage("خطأ في تحميل العقارات"),
        "errorLoadingProperty":
            MessageLookupByLibrary.simpleMessage("خطأ في تحميل العقار"),
        "errorPersistsContact": MessageLookupByLibrary.simpleMessage(
            "إذا استمر الخطأ، يرجى التواصل مع الدعم"),
        "eventNotifications":
            MessageLookupByLibrary.simpleMessage("إشعارات الأحداث"),
        "excellent": MessageLookupByLibrary.simpleMessage("ممتاز"),
        "executeAction": MessageLookupByLibrary.simpleMessage("تنفيذ الإجراء"),
        "exploreAllCategoriesSubtitle":
            MessageLookupByLibrary.simpleMessage("استكشف جميع الفئات المتاحة"),
        "exploreCategories":
            MessageLookupByLibrary.simpleMessage("تصفح الأقسام"),
        "exploreProperties":
            MessageLookupByLibrary.simpleMessage("استكشف العقارات"),
        "exportAll": MessageLookupByLibrary.simpleMessage("تصدير الكل"),
        "exportAllDescription":
            MessageLookupByLibrary.simpleMessage("تصدير بيانات العقارات"),
        "exportData": MessageLookupByLibrary.simpleMessage("تصدير البيانات"),
        "facilities": MessageLookupByLibrary.simpleMessage("المرافق"),
        "facilitiesRequired": MessageLookupByLibrary.simpleMessage(
            "يرجى اختيار مرفق واحد على الأقل"),
        "failedToCreateItem":
            MessageLookupByLibrary.simpleMessage("فشل إنشاء العنصر"),
        "failedToLoadCategories":
            MessageLookupByLibrary.simpleMessage("فشل تحميل الأقسام"),
        "failedToLoadFacilities":
            MessageLookupByLibrary.simpleMessage("فشل تحميل الخدمات"),
        "failedToLoadReels":
            MessageLookupByLibrary.simpleMessage("فشل في تحميل الريلز"),
        "failedToLoadVideo":
            MessageLookupByLibrary.simpleMessage("فشل في تحميل الفيديو"),
        "fair": MessageLookupByLibrary.simpleMessage("مقبول"),
        "familyFriendly":
            MessageLookupByLibrary.simpleMessage("مناسب للعائلات"),
        "favoriteCount": MessageLookupByLibrary.simpleMessage("المفضلة"),
        "featuredPlaces": MessageLookupByLibrary.simpleMessage("أماكن مميزة"),
        "female": MessageLookupByLibrary.simpleMessage("أنثى"),
        "filter": MessageLookupByLibrary.simpleMessage("تصفية"),
        "filterReels": MessageLookupByLibrary.simpleMessage("تصفية الريلز"),
        "filterResults": MessageLookupByLibrary.simpleMessage("تصفية النتائج"),
        "finalPrice": MessageLookupByLibrary.simpleMessage("السعر النهائي"),
        "flexiblePolicy": MessageLookupByLibrary.simpleMessage("مرنة"),
        "foundHelpful":
            MessageLookupByLibrary.simpleMessage("شخص وجد هذا التقييم مفيداً"),
        "freeParking": MessageLookupByLibrary.simpleMessage("موقف مجاني"),
        "freeWifi": MessageLookupByLibrary.simpleMessage("واي فاي مجاني"),
        "freeWifiArabic": MessageLookupByLibrary.simpleMessage("واي فاي مجاني"),
        "fullName": MessageLookupByLibrary.simpleMessage("الاسم الكامل"),
        "gallery": MessageLookupByLibrary.simpleMessage("المعرض"),
        "garden": MessageLookupByLibrary.simpleMessage("حديقة"),
        "gardenView": MessageLookupByLibrary.simpleMessage("إطلالة الحديقة"),
        "gatherPoint": MessageLookupByLibrary.simpleMessage("نقطة تجمع"),
        "gender": MessageLookupByLibrary.simpleMessage("الجنس"),
        "goBack": MessageLookupByLibrary.simpleMessage("العودة"),
        "good": MessageLookupByLibrary.simpleMessage("جيد"),
        "gridView": MessageLookupByLibrary.simpleMessage("عرض الشبكة"),
        "guestComment": MessageLookupByLibrary.simpleMessage("تعليق الضيف"),
        "guestFavorite": MessageLookupByLibrary.simpleMessage("مفضل الضيوف"),
        "guestName": MessageLookupByLibrary.simpleMessage("اسم الضيف"),
        "guestRating": MessageLookupByLibrary.simpleMessage("تقييم الضيف"),
        "guestReview": MessageLookupByLibrary.simpleMessage("تقييم الضيف"),
        "guests": MessageLookupByLibrary.simpleMessage("ضيوف"),
        "guestsInvalid": MessageLookupByLibrary.simpleMessage(
            "عدد الضيوف يجب أن يكون بين 1 و 20"),
        "guestsRequired":
            MessageLookupByLibrary.simpleMessage("عدد الضيوف مطلوب"),
        "gym": MessageLookupByLibrary.simpleMessage("صالة رياضية"),
        "heating": MessageLookupByLibrary.simpleMessage("تدفئة"),
        "hideComments": MessageLookupByLibrary.simpleMessage("إخفاء التعليقات"),
        "highRated": MessageLookupByLibrary.simpleMessage("عالي التقييم"),
        "home": MessageLookupByLibrary.simpleMessage("الرئيسية"),
        "hostDashboard": MessageLookupByLibrary.simpleMessage("لوحة المضيف"),
        "hostMode": MessageLookupByLibrary.simpleMessage("وضع المستضيف"),
        "hostModeDescription": MessageLookupByLibrary.simpleMessage(
            "قم بتفعيل وضع المستضيف لإدارة عقاراتك"),
        "hostName": MessageLookupByLibrary.simpleMessage("اسم المضيف"),
        "hostedBy": MessageLookupByLibrary.simpleMessage("مستضاف من قبل"),
        "hostingTips": MessageLookupByLibrary.simpleMessage("نصائح الاستضافة"),
        "houseRules": MessageLookupByLibrary.simpleMessage("قواعد المنزل"),
        "imageGallery": MessageLookupByLibrary.simpleMessage("معرض الصور"),
        "imagesMinimum":
            MessageLookupByLibrary.simpleMessage("يرجى إضافة 3 صور على الأقل"),
        "imagesRequired": MessageLookupByLibrary.simpleMessage(
            "يرجى إضافة صورة واحدة على الأقل"),
        "importantInfo": MessageLookupByLibrary.simpleMessage("معلومات مهمة"),
        "inHosting": MessageLookupByLibrary.simpleMessage("في الاستضافة"),
        "inactive": MessageLookupByLibrary.simpleMessage("غير نشطة"),
        "inactiveListings":
            MessageLookupByLibrary.simpleMessage("العقارات غير النشطة"),
        "inactiveStatusDescription":
            MessageLookupByLibrary.simpleMessage("عقارك مخفي عن الضيوف"),
        "increasePrices": MessageLookupByLibrary.simpleMessage("زيادة الأسعار"),
        "increasePricesDescription":
            MessageLookupByLibrary.simpleMessage("زيادة الأسعار بنسبة مئوية"),
        "insights": MessageLookupByLibrary.simpleMessage("الرؤى"),
        "instantBook": MessageLookupByLibrary.simpleMessage("حجز فوري"),
        "invalidCode": MessageLookupByLibrary.simpleMessage("كود غير صحيح"),
        "invalidDate": MessageLookupByLibrary.simpleMessage("تاريخ غير صالح"),
        "itemsSelected": MessageLookupByLibrary.simpleMessage("عناصر محددة"),
        "joinAsHost": MessageLookupByLibrary.simpleMessage("انضم كمضيف"),
        "joinAsHostSubtitle": MessageLookupByLibrary.simpleMessage(
            "من السهل بدء الاستضافة وربح إضافي"),
        "kitchen": MessageLookupByLibrary.simpleMessage("مطبخ"),
        "knowledge": MessageLookupByLibrary.simpleMessage("المعارف"),
        "lakeView": MessageLookupByLibrary.simpleMessage("إطلالة البحيرة"),
        "language": MessageLookupByLibrary.simpleMessage("اللغة"),
        "last30Days": MessageLookupByLibrary.simpleMessage("آخر 30 يوم"),
        "last6Months": MessageLookupByLibrary.simpleMessage("آخر 6 أشهر"),
        "last7Days": MessageLookupByLibrary.simpleMessage("آخر 7 أيام"),
        "last90Days": MessageLookupByLibrary.simpleMessage("آخر 90 يوم"),
        "lastMonth": MessageLookupByLibrary.simpleMessage("الشهر الماضي"),
        "lastYear": MessageLookupByLibrary.simpleMessage("السنة الماضية"),
        "latitude": MessageLookupByLibrary.simpleMessage("خط العرض"),
        "laundry": MessageLookupByLibrary.simpleMessage("غسيل"),
        "leaveReview": MessageLookupByLibrary.simpleMessage("اترك تقييم"),
        "legal": MessageLookupByLibrary.simpleMessage("قانوني"),
        "listView": MessageLookupByLibrary.simpleMessage("عرض القائمة"),
        "listingStatus": MessageLookupByLibrary.simpleMessage("حالة العقار"),
        "listingsSelected":
            MessageLookupByLibrary.simpleMessage("عقارات محددة"),
        "listingsWillBeAffected":
            MessageLookupByLibrary.simpleMessage("عقارات ستتأثر"),
        "loading": MessageLookupByLibrary.simpleMessage("جاري التحميل..."),
        "loadingPropertyData":
            MessageLookupByLibrary.simpleMessage("جاري تحميل بيانات العقار..."),
        "loadingReels":
            MessageLookupByLibrary.simpleMessage("جاري تحميل الريلز..."),
        "location": MessageLookupByLibrary.simpleMessage("الموقع"),
        "locationAndAddress":
            MessageLookupByLibrary.simpleMessage("الموقع والعنوان"),
        "locationPermissionError": MessageLookupByLibrary.simpleMessage(
            "يرجى تفعيل إذن الموقع لاستخدام التطبيق"),
        "locationRequired":
            MessageLookupByLibrary.simpleMessage("يرجى اختيار موقع"),
        "login": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
        "logout": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
        "logoutConfirmation": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد أنك تريد تسجيل الخروج؟"),
        "longTermBookings": MessageLookupByLibrary.simpleMessage(
            "الحجوزات طويلة المدى (>28 يوم)"),
        "longitude": MessageLookupByLibrary.simpleMessage("خط الطول"),
        "luxuryStay": MessageLookupByLibrary.simpleMessage("إقامة فاخرة"),
        "mainImage": MessageLookupByLibrary.simpleMessage("الصورة الرئيسية"),
        "male": MessageLookupByLibrary.simpleMessage("ذكر"),
        "manageNotifications":
            MessageLookupByLibrary.simpleMessage("إدارة إعدادات الإشعارات"),
        "managementActions":
            MessageLookupByLibrary.simpleMessage("إجراءات الإدارة"),
        "mapView": MessageLookupByLibrary.simpleMessage("عرض الخريطة"),
        "march2024": MessageLookupByLibrary.simpleMessage("مارس 2024"),
        "marketingNotifications":
            MessageLookupByLibrary.simpleMessage("الإشعارات التسويقية"),
        "maxGuests": MessageLookupByLibrary.simpleMessage("الحد الأقصى للضيوف"),
        "media": MessageLookupByLibrary.simpleMessage("الوسائط"),
        "messageNotifications":
            MessageLookupByLibrary.simpleMessage("إشعارات الرسائل"),
        "minimumRating": MessageLookupByLibrary.simpleMessage("التقييم الأدنى"),
        "minimumWithdraw":
            MessageLookupByLibrary.simpleMessage("الحد الأدنى للسحب: ر.س 50"),
        "minimumWithdrawAmount":
            MessageLookupByLibrary.simpleMessage("الحد الأدنى للسحب: ر.س 50"),
        "moderatePolicy": MessageLookupByLibrary.simpleMessage("معتدلة"),
        "modifyReservation":
            MessageLookupByLibrary.simpleMessage("تعديل الحجز"),
        "monthlyPrice": MessageLookupByLibrary.simpleMessage("السعر الشهري"),
        "moreActions":
            MessageLookupByLibrary.simpleMessage("المزيد من الإجراءات"),
        "mostCommented": MessageLookupByLibrary.simpleMessage("الأكثر تعليقاً"),
        "mostLiked": MessageLookupByLibrary.simpleMessage("الأكثر إعجاباً"),
        "mountainView": MessageLookupByLibrary.simpleMessage("إطلالة جبلية"),
        "mustLogin":
            MessageLookupByLibrary.simpleMessage("يجب عليك تسجيل الدخول"),
        "mustLoginDescription": MessageLookupByLibrary.simpleMessage(
            "قم بتسجيل الدخول لعرض حسابك الشخصي"),
        "muteVideo": MessageLookupByLibrary.simpleMessage("كتم الصوت"),
        "myBookings": MessageLookupByLibrary.simpleMessage("حجوزاتي"),
        "myListings": MessageLookupByLibrary.simpleMessage("عقاراتي"),
        "nearbyAttractions":
            MessageLookupByLibrary.simpleMessage("المعالم القريبة"),
        "nearbyPlaces": MessageLookupByLibrary.simpleMessage("الأماكن القريبة"),
        "needHelp": MessageLookupByLibrary.simpleMessage("تحتاج مساعدة؟"),
        "needsAttention": MessageLookupByLibrary.simpleMessage("تحتاج اهتمام"),
        "netRevenue": MessageLookupByLibrary.simpleMessage("صافي الإيرادات"),
        "newEventsAndUpdates": MessageLookupByLibrary.simpleMessage(
            "إشعارات حول الأحداث الجديدة والتحديثات"),
        "newHost": MessageLookupByLibrary.simpleMessage("مستضيف جديد"),
        "newListing": MessageLookupByLibrary.simpleMessage("إعلان جديد"),
        "newMessagesAndChats": MessageLookupByLibrary.simpleMessage(
            "إشعارات الرسائل الجديدة والمحادثات"),
        "newPrice": MessageLookupByLibrary.simpleMessage("السعر الجديد"),
        "newest": MessageLookupByLibrary.simpleMessage("الأحدث"),
        "next": MessageLookupByLibrary.simpleMessage("التالي"),
        "night": MessageLookupByLibrary.simpleMessage("ليلة"),
        "nights": MessageLookupByLibrary.simpleMessage("ليالي"),
        "nightsStayed": MessageLookupByLibrary.simpleMessage("عدد الليالي"),
        "noAmenitiesListed":
            MessageLookupByLibrary.simpleMessage("لا توجد مرافق مدرجة"),
        "noBookingsMessage":
            MessageLookupByLibrary.simpleMessage("لا توجد حجوزات حديثة"),
        "noBookingsSubtitle":
            MessageLookupByLibrary.simpleMessage("لم تقم بأي حجوزات حتى الآن"),
        "noBookingsYet": MessageLookupByLibrary.simpleMessage("لا توجد حجوزات"),
        "noComments": MessageLookupByLibrary.simpleMessage("لا توجد تعليقات"),
        "noData": MessageLookupByLibrary.simpleMessage("لا توجد بيانات"),
        "noDataAvailable":
            MessageLookupByLibrary.simpleMessage("لا توجد بيانات متاحة"),
        "noDescription": MessageLookupByLibrary.simpleMessage("بدون وصف"),
        "noDescriptionAvailable":
            MessageLookupByLibrary.simpleMessage("لا يوجد وصف متاح."),
        "noListingsDescription": MessageLookupByLibrary.simpleMessage(
            "ابدأ رحلة الاستضافة بإنشاء أول عقار لك. شارك مساحتك مع المسافرين وابدأ في الكسب!"),
        "noListingsYet":
            MessageLookupByLibrary.simpleMessage("لا توجد عقارات بعد"),
        "noPropertiesSubtitle":
            MessageLookupByLibrary.simpleMessage("ابدأ بإضافة عقارك الأول"),
        "noPropertiesYet":
            MessageLookupByLibrary.simpleMessage("لا توجد عقارات"),
        "noRecentBookings":
            MessageLookupByLibrary.simpleMessage("لا توجد حجوزات حديثة"),
        "noRecentReviews":
            MessageLookupByLibrary.simpleMessage("لا توجد تعليقات حديثة"),
        "noResults": MessageLookupByLibrary.simpleMessage("لا توجد نتائج"),
        "noResultsFound": MessageLookupByLibrary.simpleMessage("لا توجد نتائج"),
        "noReviewsFound":
            MessageLookupByLibrary.simpleMessage("لا توجد تقييمات"),
        "noReviewsMatchFilter": MessageLookupByLibrary.simpleMessage(
            "لا توجد تقييمات تطابق الفلتر المحدد"),
        "noReviewsMessage":
            MessageLookupByLibrary.simpleMessage("لا توجد تعليقات حديثة"),
        "noSearchResults":
            MessageLookupByLibrary.simpleMessage("لا توجد نتائج بحث"),
        "noSmoking": MessageLookupByLibrary.simpleMessage("ممنوع التدخين"),
        "noTitle": MessageLookupByLibrary.simpleMessage("بدون عنوان"),
        "noView": MessageLookupByLibrary.simpleMessage("بدون إطلالة"),
        "normalDays":
            MessageLookupByLibrary.simpleMessage("أيام العمل العادية"),
        "notAvailable": MessageLookupByLibrary.simpleMessage("غير متوفر"),
        "notSpecified": MessageLookupByLibrary.simpleMessage("غير محدد"),
        "notificationPermissionRequired":
            MessageLookupByLibrary.simpleMessage("مطلوب إذن الإشعارات"),
        "notificationSettings":
            MessageLookupByLibrary.simpleMessage("إعدادات الإشعارات"),
        "notifications": MessageLookupByLibrary.simpleMessage("الإشعارات"),
        "numberOfBathrooms":
            MessageLookupByLibrary.simpleMessage("عدد الحمامات"),
        "numberOfBedrooms":
            MessageLookupByLibrary.simpleMessage("عدد غرف النوم"),
        "numberOfDays": MessageLookupByLibrary.simpleMessage("عدد الأيام"),
        "numberOfGuests": MessageLookupByLibrary.simpleMessage("عدد الضيوف"),
        "occupancyRate": MessageLookupByLibrary.simpleMessage("معدل الإشغال"),
        "oceanView": MessageLookupByLibrary.simpleMessage("إطلالة المحيط"),
        "ofPreposition": MessageLookupByLibrary.simpleMessage("من"),
        "offersAndMarketing": MessageLookupByLibrary.simpleMessage(
            "إشعارات العروض والأخبار التسويقية"),
        "ok": MessageLookupByLibrary.simpleMessage("موافق"),
        "oldest": MessageLookupByLibrary.simpleMessage("الأقدم"),
        "openSettings": MessageLookupByLibrary.simpleMessage("فتح الإعدادات"),
        "or": MessageLookupByLibrary.simpleMessage("أو"),
        "overview": MessageLookupByLibrary.simpleMessage("نظرة عامة"),
        "partyFriendly": MessageLookupByLibrary.simpleMessage("مناسب للحفلات"),
        "pauseVideo": MessageLookupByLibrary.simpleMessage("إيقاف الفيديو"),
        "paypal": MessageLookupByLibrary.simpleMessage("باي بال"),
        "pending": MessageLookupByLibrary.simpleMessage("قيد الانتظار"),
        "pendingEarnings":
            MessageLookupByLibrary.simpleMessage("الأرباح المعلقة"),
        "pendingReservations":
            MessageLookupByLibrary.simpleMessage("حجوزات معلقة"),
        "pendingReview": MessageLookupByLibrary.simpleMessage("قيد المراجعة"),
        "pendingStatusDescription": MessageLookupByLibrary.simpleMessage(
            "عقارك قيد المراجعة من فريقنا"),
        "perNight": MessageLookupByLibrary.simpleMessage("ر.س / الليلة"),
        "percentage": MessageLookupByLibrary.simpleMessage("النسبة المئوية"),
        "performanceGrade":
            MessageLookupByLibrary.simpleMessage("تقييم الأداء"),
        "personalInfo":
            MessageLookupByLibrary.simpleMessage("المعلومات الشخصية"),
        "petFriendly":
            MessageLookupByLibrary.simpleMessage("مناسب للحيوانات الأليفة"),
        "phone": MessageLookupByLibrary.simpleMessage("رقم الجوال"),
        "phoneNumberHint": MessageLookupByLibrary.simpleMessage("5xxxxxxxx"),
        "photosAndVideo":
            MessageLookupByLibrary.simpleMessage("الصور والفيديو"),
        "pickLocation": MessageLookupByLibrary.simpleMessage("اختر الموقع"),
        "playVideo": MessageLookupByLibrary.simpleMessage("تشغيل الفيديو"),
        "pleaseBathrooms":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال عدد الحمامات"),
        "pleaseCheckPhoneNumber":
            MessageLookupByLibrary.simpleMessage("الرجاء التحقق من رقم الجوال"),
        "pleaseEnterBedrooms":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال عدد غرف النوم"),
        "pleaseEnterDescription":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال وصف العقار"),
        "pleaseEnterMaxGuests": MessageLookupByLibrary.simpleMessage(
            "يرجى إدخال الحد الأقصى للضيوف"),
        "pleaseEnterPhoneNumber":
            MessageLookupByLibrary.simpleMessage("الرجاء ادخال رقم الجوال"),
        "pleaseEnterPrice":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال السعر"),
        "pleaseEnterPropertyTitle":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال عنوان العقار"),
        "pleaseEnterTitle":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال عنوان العقار"),
        "pleaseEnterValidNumber":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال رقم صحيح"),
        "pleaseEnterValidPrice":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال سعر صحيح"),
        "pleaseSelectBothDates":
            MessageLookupByLibrary.simpleMessage("يرجى اختيار كلا التاريخين"),
        "pleaseSelectCancellationPolicy":
            MessageLookupByLibrary.simpleMessage("يرجى اختيار سياسة الإلغاء"),
        "pleaseSelectCategory":
            MessageLookupByLibrary.simpleMessage("يرجى اختيار الفئة"),
        "pleaseSelectPropertyType":
            MessageLookupByLibrary.simpleMessage("يرجى اختيار نوع العقار"),
        "policies": MessageLookupByLibrary.simpleMessage("السياسات"),
        "policyDescription":
            MessageLookupByLibrary.simpleMessage("وصف السياسة"),
        "pool": MessageLookupByLibrary.simpleMessage("مسبح"),
        "poor": MessageLookupByLibrary.simpleMessage("ضعيف"),
        "popular": MessageLookupByLibrary.simpleMessage("الأكثر شعبية"),
        "popularDestinations":
            MessageLookupByLibrary.simpleMessage("الوجهات الشائعة"),
        "popularPlaces":
            MessageLookupByLibrary.simpleMessage("الأماكن الشائعة"),
        "postComment": MessageLookupByLibrary.simpleMessage("نشر التعليق"),
        "previousTrip": MessageLookupByLibrary.simpleMessage("رحلة سابقة"),
        "previousTrips":
            MessageLookupByLibrary.simpleMessage("الرحلات السابقة"),
        "price": MessageLookupByLibrary.simpleMessage("السعر"),
        "priceBredown": MessageLookupByLibrary.simpleMessage("تفاصيل الأسعار"),
        "priceDetails": MessageLookupByLibrary.simpleMessage("تفاصيل السعر"),
        "priceHighToLow":
            MessageLookupByLibrary.simpleMessage("السعر: الأعلى أولاً"),
        "priceInvalid":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال سعر صحيح"),
        "priceLowToHigh":
            MessageLookupByLibrary.simpleMessage("السعر: الأقل أولاً"),
        "priceMinimum": MessageLookupByLibrary.simpleMessage(
            "الحد الأدنى للسعر 50 ريال سعودي في الليلة"),
        "pricePerNight":
            MessageLookupByLibrary.simpleMessage("السعر لليلة الواحدة"),
        "priceRange": MessageLookupByLibrary.simpleMessage("نطاق السعر"),
        "priceRequired": MessageLookupByLibrary.simpleMessage("السعر مطلوب"),
        "priceType": MessageLookupByLibrary.simpleMessage("نوع السعر"),
        "priceWithCurrency": m0,
        "pricing": MessageLookupByLibrary.simpleMessage("التسعير"),
        "pricingActions":
            MessageLookupByLibrary.simpleMessage("إجراءات التسعير"),
        "privacy": MessageLookupByLibrary.simpleMessage("الخصوصية والأمان"),
        "privacyAndSecurity":
            MessageLookupByLibrary.simpleMessage("إعدادات الخصوصية والأمان"),
        "privacySettings":
            MessageLookupByLibrary.simpleMessage("إعدادات الخصوصية والحماية"),
        "proceedAsGuest": MessageLookupByLibrary.simpleMessage("متابعة كضيف"),
        "proceedLabel": MessageLookupByLibrary.simpleMessage("استمرار"),
        "proceedWithApple":
            MessageLookupByLibrary.simpleMessage("المتابعة مع آبل"),
        "proceedWithGoogle":
            MessageLookupByLibrary.simpleMessage("المتابعة مع جوجل"),
        "proceedWithPhone":
            MessageLookupByLibrary.simpleMessage("تابع بأستخدام رقم جوالك"),
        "processing": MessageLookupByLibrary.simpleMessage("قيد المعالجة"),
        "profile": MessageLookupByLibrary.simpleMessage("حسابي"),
        "profileImage": MessageLookupByLibrary.simpleMessage("الصورة الشخصية"),
        "properties": MessageLookupByLibrary.simpleMessage("العقارات"),
        "propertyCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("تم إنشاء العقار بنجاح!"),
        "propertyDescription":
            MessageLookupByLibrary.simpleMessage("وصف العقار"),
        "propertyDescriptionRequired":
            MessageLookupByLibrary.simpleMessage("وصف العقار مطلوب"),
        "propertyDescriptionTooShort": MessageLookupByLibrary.simpleMessage(
            "وصف العقار يجب أن يكون 10 أحرف على الأقل"),
        "propertyDetails":
            MessageLookupByLibrary.simpleMessage("تفاصيل العقار"),
        "propertyImages": MessageLookupByLibrary.simpleMessage("صور العقار"),
        "propertyLocation": MessageLookupByLibrary.simpleMessage("موقع العقار"),
        "propertyName": MessageLookupByLibrary.simpleMessage("اسم العقار"),
        "propertyNotFound":
            MessageLookupByLibrary.simpleMessage("العقار غير موجود"),
        "propertyNotFoundDescription": MessageLookupByLibrary.simpleMessage(
            "العقار الذي تبحث عنه غير موجود أو تم حذفه."),
        "propertyPreview":
            MessageLookupByLibrary.simpleMessage("معاينة العقار"),
        "propertyTitle": MessageLookupByLibrary.simpleMessage("عنوان العقار"),
        "propertyTitleRequired":
            MessageLookupByLibrary.simpleMessage("عنوان العقار مطلوب"),
        "propertyTitleTooShort": MessageLookupByLibrary.simpleMessage(
            "عنوان العقار يجب أن يكون 3 أحرف على الأقل"),
        "propertyType": MessageLookupByLibrary.simpleMessage("نوع العقار"),
        "propertyTypeRequired":
            MessageLookupByLibrary.simpleMessage("يرجى اختيار نوع العقار"),
        "propertyUpdatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("تم تحديث العقار بنجاح"),
        "publishAll": MessageLookupByLibrary.simpleMessage("نشر الكل"),
        "publishAllDescription":
            MessageLookupByLibrary.simpleMessage("نشر جميع العقارات المحددة"),
        "publishListing": MessageLookupByLibrary.simpleMessage("نشر العقار"),
        "publishListingConfirmation": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد من نشر هذا العقار؟"),
        "publishProperty": MessageLookupByLibrary.simpleMessage("نشر العقار"),
        "pushNotifications":
            MessageLookupByLibrary.simpleMessage("الإشعارات الفورية"),
        "rareFind": MessageLookupByLibrary.simpleMessage("اكتشاف نادر"),
        "rating": MessageLookupByLibrary.simpleMessage("التقييم"),
        "ratingHighToLow":
            MessageLookupByLibrary.simpleMessage("التقييم: الأعلى أولاً"),
        "ratingLowToHigh":
            MessageLookupByLibrary.simpleMessage("التقييم: الأقل أولاً"),
        "rebookProperty": MessageLookupByLibrary.simpleMessage("إعادة الحجز"),
        "recentBookings": MessageLookupByLibrary.simpleMessage("أحدث الحجوزات"),
        "recentReviews": MessageLookupByLibrary.simpleMessage("أحدث التعليقات"),
        "reels": MessageLookupByLibrary.simpleMessage("الريلز"),
        "referHost": MessageLookupByLibrary.simpleMessage("إحالة مضيف"),
        "rejectionReason": MessageLookupByLibrary.simpleMessage("سبب الرفض"),
        "removeFromFavorites":
            MessageLookupByLibrary.simpleMessage("إزالة من المفضلة"),
        "replyToComment":
            MessageLookupByLibrary.simpleMessage("رد على التعليق"),
        "requestHelp": MessageLookupByLibrary.simpleMessage("اطلب المساعدة"),
        "resendCode": MessageLookupByLibrary.simpleMessage("إعادة إرسال الكود"),
        "reservationConfirmed":
            MessageLookupByLibrary.simpleMessage("تم تأكيد الحجز بنجاح!"),
        "reservationFailed":
            MessageLookupByLibrary.simpleMessage("فشل تأكيد الحجز"),
        "reservationFrom":
            MessageLookupByLibrary.simpleMessage("تاريخ الحجز من"),
        "reservationTo":
            MessageLookupByLibrary.simpleMessage("تاريخ الحجز إلى"),
        "reservations": MessageLookupByLibrary.simpleMessage("الحجوزات"),
        "reserve": MessageLookupByLibrary.simpleMessage("احجز"),
        "resetFilters": MessageLookupByLibrary.simpleMessage("إعادة تعيين"),
        "responseRate": MessageLookupByLibrary.simpleMessage("معدل الاستجابة"),
        "responseTime": MessageLookupByLibrary.simpleMessage("وقت الاستجابة"),
        "restaurant": MessageLookupByLibrary.simpleMessage("مطعم"),
        "retry": MessageLookupByLibrary.simpleMessage("إعادة المحاولة"),
        "revenueMetrics":
            MessageLookupByLibrary.simpleMessage("مقاييس الإيرادات"),
        "reviewAndSubmit":
            MessageLookupByLibrary.simpleMessage("المراجعة والإرسال"),
        "reviewReservation":
            MessageLookupByLibrary.simpleMessage("مراجعة الحجز"),
        "reviewSubmittedSuccessfully":
            MessageLookupByLibrary.simpleMessage("تم إرسال التقييم بنجاح"),
        "reviews": MessageLookupByLibrary.simpleMessage("التقييمات"),
        "reviewsCount": MessageLookupByLibrary.simpleMessage("تقييم"),
        "reviewsOverview":
            MessageLookupByLibrary.simpleMessage("نظرة عامة على التقييمات"),
        "rooms": MessageLookupByLibrary.simpleMessage("غرف"),
        "rules": MessageLookupByLibrary.simpleMessage("القواعد"),
        "safetyFeatures": MessageLookupByLibrary.simpleMessage("ميزات الأمان"),
        "sampleReviewText": MessageLookupByLibrary.simpleMessage(
            "مكان رائع للإقامة! نظيف ومريح وكما هو موصوف تماماً. المضيف كان متجاوباً ومفيداً جداً."),
        "sar": MessageLookupByLibrary.simpleMessage("ر.س"),
        "sarPerNight": MessageLookupByLibrary.simpleMessage("ر.س/ليلة"),
        "saveAsDraft": MessageLookupByLibrary.simpleMessage("حفظ كمسودة"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("حفظ التغييرات"),
        "savingProperty":
            MessageLookupByLibrary.simpleMessage("جاري حفظ العقار..."),
        "search": MessageLookupByLibrary.simpleMessage("البحث"),
        "searchHint":
            MessageLookupByLibrary.simpleMessage("ابحث عن وجهتك المفضلة..."),
        "searchListings":
            MessageLookupByLibrary.simpleMessage("البحث في العقارات..."),
        "searchPlaceholder":
            MessageLookupByLibrary.simpleMessage("حياك ... دور علي اللي تبيه"),
        "searchReels": MessageLookupByLibrary.simpleMessage("البحث في الريلز"),
        "searchResults": MessageLookupByLibrary.simpleMessage("نتائج البحث"),
        "searching": MessageLookupByLibrary.simpleMessage("جاري البحث..."),
        "seeAll": MessageLookupByLibrary.simpleMessage("عرض الكل"),
        "selectAction": MessageLookupByLibrary.simpleMessage("اختر الإجراء"),
        "selectActionCategory":
            MessageLookupByLibrary.simpleMessage("اختر فئة الإجراء"),
        "selectAll": MessageLookupByLibrary.simpleMessage("تحديد الكل"),
        "selectBirthdate":
            MessageLookupByLibrary.simpleMessage("اختر تاريخ الميلاد"),
        "selectCancellationPolicy":
            MessageLookupByLibrary.simpleMessage("اختر سياسة الإلغاء"),
        "selectCategory": MessageLookupByLibrary.simpleMessage("اختر القسم"),
        "selectCity": MessageLookupByLibrary.simpleMessage("اختر المدينة"),
        "selectLocation": MessageLookupByLibrary.simpleMessage("تحديد الموقع"),
        "selectMultiple": MessageLookupByLibrary.simpleMessage("تحديد متعدد"),
        "selectNewStatus":
            MessageLookupByLibrary.simpleMessage("اختر الحالة الجديدة"),
        "selectReservationDate":
            MessageLookupByLibrary.simpleMessage("اختيار تاريخ الحجز"),
        "selectServices":
            MessageLookupByLibrary.simpleMessage("اختر الخدمات المتوفرة"),
        "selectedPeriodNotAvailable": MessageLookupByLibrary.simpleMessage(
            "الفترة المحددة غير متاحة، يرجى اختيار فترة أخرى."),
        "sendTestNotification":
            MessageLookupByLibrary.simpleMessage("إرسال إشعار تجريبي"),
        "serviceFee": MessageLookupByLibrary.simpleMessage("رسوم الخدمة"),
        "setPrices": MessageLookupByLibrary.simpleMessage("تحديد الأسعار"),
        "setPricesDescription": MessageLookupByLibrary.simpleMessage(
            "تحديد سعر ثابت لجميع العقارات"),
        "settings": MessageLookupByLibrary.simpleMessage("الإعدادات"),
        "share": MessageLookupByLibrary.simpleMessage("مشاركة"),
        "shareCount": MessageLookupByLibrary.simpleMessage("المشاركات"),
        "shareProperty": MessageLookupByLibrary.simpleMessage("مشاركة العقار"),
        "shortTermBookings": MessageLookupByLibrary.simpleMessage(
            "الحجوزات قصيرة المدى (≤28 يوم)"),
        "showAllAmenities":
            MessageLookupByLibrary.simpleMessage("عرض جميع المرافق"),
        "showAllReviews": MessageLookupByLibrary.simpleMessage("عرض جميع"),
        "showComments": MessageLookupByLibrary.simpleMessage("عرض التعليقات"),
        "showLess": MessageLookupByLibrary.simpleMessage("عرض أقل"),
        "showMore": MessageLookupByLibrary.simpleMessage("عرض المزيد"),
        "since": MessageLookupByLibrary.simpleMessage("منذ"),
        "skip": MessageLookupByLibrary.simpleMessage("تخطي"),
        "smartEntry": MessageLookupByLibrary.simpleMessage("دخول ذكي"),
        "smokingAllowed": MessageLookupByLibrary.simpleMessage("التدخين مسموح"),
        "sortBy": MessageLookupByLibrary.simpleMessage("ترتيب حسب"),
        "soundClick": MessageLookupByLibrary.simpleMessage("صوت النقرات"),
        "soundClickDisabled":
            MessageLookupByLibrary.simpleMessage("تم إيقاف أصوات النقر"),
        "soundClickEnabled":
            MessageLookupByLibrary.simpleMessage("تم تفعيل أصوات النقر"),
        "soundScroll": MessageLookupByLibrary.simpleMessage("صوت التمرير"),
        "soundScrollDisabled":
            MessageLookupByLibrary.simpleMessage("تم إيقاف أصوات التمرير"),
        "soundScrollEnabled":
            MessageLookupByLibrary.simpleMessage("تم تفعيل أصوات التمرير"),
        "soundSettings": MessageLookupByLibrary.simpleMessage("إعدادات الصوت"),
        "spa": MessageLookupByLibrary.simpleMessage("سبا"),
        "start": MessageLookupByLibrary.simpleMessage("ابدأ"),
        "status": MessageLookupByLibrary.simpleMessage("الحالة"),
        "statusActions": MessageLookupByLibrary.simpleMessage("إجراءات الحالة"),
        "statusChangeError":
            MessageLookupByLibrary.simpleMessage("خطأ في تغيير الحالة"),
        "statusChangedSuccessfully":
            MessageLookupByLibrary.simpleMessage("تم تغيير الحالة بنجاح"),
        "streetView": MessageLookupByLibrary.simpleMessage("إطلالة الشارع"),
        "strictPolicy": MessageLookupByLibrary.simpleMessage("مشددة"),
        "submit": MessageLookupByLibrary.simpleMessage("إرسال"),
        "submitReview": MessageLookupByLibrary.simpleMessage("إرسال التقييم"),
        "superhost": MessageLookupByLibrary.simpleMessage("مستضيف ممتاز"),
        "suspendedStatusDescription":
            MessageLookupByLibrary.simpleMessage("تم تعليق عقارك"),
        "tapToChangeImage":
            MessageLookupByLibrary.simpleMessage("اضغط لتغيير الصورة"),
        "tapToUploadImages":
            MessageLookupByLibrary.simpleMessage("اضغط لرفع الصور"),
        "taxes": MessageLookupByLibrary.simpleMessage("الضرائب"),
        "testNotification":
            MessageLookupByLibrary.simpleMessage("إشعار تجريبي"),
        "themeDisabled":
            MessageLookupByLibrary.simpleMessage("تم تفعيل الوضع الفاتح"),
        "themeEnabled":
            MessageLookupByLibrary.simpleMessage("تم تفعيل الوضع المظلم"),
        "thirdPartySharing":
            MessageLookupByLibrary.simpleMessage("المشاركة مع الأطراف الثالثة"),
        "thirdPartySharingDesc": MessageLookupByLibrary.simpleMessage(
            "معلومات حول مشاركة البيانات مع الشركاء"),
        "thisMonth": MessageLookupByLibrary.simpleMessage("هذا الشهر"),
        "thisYear": MessageLookupByLibrary.simpleMessage("هذا العام"),
        "tip1": MessageLookupByLibrary.simpleMessage(
            "أضف صور عالية الجودة لجذب المزيد من الضيوف"),
        "tip2":
            MessageLookupByLibrary.simpleMessage("اكتب وصفاً مفصلاً لعقارك"),
        "tip3":
            MessageLookupByLibrary.simpleMessage("حدد أسعاراً تنافسية لمنطقتك"),
        "titleAndDescription":
            MessageLookupByLibrary.simpleMessage("العنوان والوصف"),
        "topPerforming": MessageLookupByLibrary.simpleMessage("الأفضل أداءً"),
        "topRated": MessageLookupByLibrary.simpleMessage("الأعلى تقييماً"),
        "total": MessageLookupByLibrary.simpleMessage("الإجمالي"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("المبلغ الإجمالي"),
        "totalBookings":
            MessageLookupByLibrary.simpleMessage("إجمالي الحجوزات"),
        "totalEarnings": MessageLookupByLibrary.simpleMessage("إجمالي الأرباح"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("إجمالي السعر"),
        "totalProperties":
            MessageLookupByLibrary.simpleMessage("إجمالي العقارات"),
        "totalReservations":
            MessageLookupByLibrary.simpleMessage("إجمالي الحجوزات"),
        "totalRevenue":
            MessageLookupByLibrary.simpleMessage("إجمالي الإيرادات"),
        "totalReviews":
            MessageLookupByLibrary.simpleMessage("إجمالي التقييمات"),
        "totalValue": MessageLookupByLibrary.simpleMessage("القيمة الإجمالية"),
        "totalViews": MessageLookupByLibrary.simpleMessage("إجمالي المشاهدات"),
        "totalWithdrawn":
            MessageLookupByLibrary.simpleMessage("إجمالي المسحوب"),
        "tourismPermitNumber":
            MessageLookupByLibrary.simpleMessage("تصريح وزارة السياحة رقم"),
        "transportation": MessageLookupByLibrary.simpleMessage("المواصلات"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("حاول مرة أخرى"),
        "tryDifferentKeywords":
            MessageLookupByLibrary.simpleMessage("جرب البحث بكلمات مختلفة"),
        "tryDifferentSearch":
            MessageLookupByLibrary.simpleMessage("جرب البحث بكلمات مختلفة"),
        "tryDifferentSearchCriteria":
            MessageLookupByLibrary.simpleMessage("جرب تعديل معايير البحث"),
        "tv": MessageLookupByLibrary.simpleMessage("تلفزيون"),
        "underReview": MessageLookupByLibrary.simpleMessage("قيد المراجعة"),
        "uniqueViews":
            MessageLookupByLibrary.simpleMessage("المشاهدات الفريدة"),
        "unitDetails": MessageLookupByLibrary.simpleMessage("تفاصيل الوحدة"),
        "unitName": MessageLookupByLibrary.simpleMessage("اسم الوحدة"),
        "unknownStatusDescription":
            MessageLookupByLibrary.simpleMessage("حالة غير معروفة"),
        "unmuteVideo": MessageLookupByLibrary.simpleMessage("إلغاء كتم الصوت"),
        "updateProperty": MessageLookupByLibrary.simpleMessage("تحديث العقار"),
        "validationFailed": MessageLookupByLibrary.simpleMessage(
            "يرجى إصلاح الأخطاء والمحاولة مرة أخرى"),
        "verificationCodeSent": MessageLookupByLibrary.simpleMessage(
            "لقد تم إرسال رمز مكون من 4 أرقام إلى جوالك"),
        "verificationFailed":
            MessageLookupByLibrary.simpleMessage("فشل التحقق"),
        "verified": MessageLookupByLibrary.simpleMessage("موثق"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("التحقق من رقم جوالك"),
        "version": MessageLookupByLibrary.simpleMessage("الإصدار"),
        "veryGood": MessageLookupByLibrary.simpleMessage("جيد جداً"),
        "video": MessageLookupByLibrary.simpleMessage("الفيديو"),
        "viewAll": MessageLookupByLibrary.simpleMessage("عرض الكل"),
        "viewAllBookings":
            MessageLookupByLibrary.simpleMessage("عرض جميع الحجوزات"),
        "viewAllReviews":
            MessageLookupByLibrary.simpleMessage("عرض جميع التقييمات"),
        "viewDetails": MessageLookupByLibrary.simpleMessage("عرض التفاصيل"),
        "viewProfile": MessageLookupByLibrary.simpleMessage("عرض الملف الشخصي"),
        "viewReservations":
            MessageLookupByLibrary.simpleMessage("عرض الحجوزات"),
        "views": MessageLookupByLibrary.simpleMessage("المشاهدات"),
        "viewsTrend": MessageLookupByLibrary.simpleMessage("اتجاه المشاهدات"),
        "walletBalance": MessageLookupByLibrary.simpleMessage("رصيد المحفظة"),
        "weekendDays":
            MessageLookupByLibrary.simpleMessage("أيام العطلة الأسبوعية"),
        "weekendPrice":
            MessageLookupByLibrary.simpleMessage("سعر عطلة نهاية الأسبوع"),
        "weeklyPrice": MessageLookupByLibrary.simpleMessage("السعر الأسبوعي"),
        "welcomeGuest": MessageLookupByLibrary.simpleMessage(
            "حيا الله ضيفنا ... متابعة كضيف"),
        "whatThisPlaceOffers":
            MessageLookupByLibrary.simpleMessage("ما يقدمه هذا المكان"),
        "whereYoullBe": MessageLookupByLibrary.simpleMessage("أين ستكون"),
        "wifi": MessageLookupByLibrary.simpleMessage("واي فاي"),
        "withdraw": MessageLookupByLibrary.simpleMessage("سحب"),
        "withdrawFunds": MessageLookupByLibrary.simpleMessage("سحب الأموال"),
        "withdrawalMethod": MessageLookupByLibrary.simpleMessage("طريقة السحب"),
        "workFriendly": MessageLookupByLibrary.simpleMessage("مناسب للعمل"),
        "workspace": MessageLookupByLibrary.simpleMessage("مساحة عمل"),
        "writeComment": MessageLookupByLibrary.simpleMessage("اكتب تعليق..."),
        "year": MessageLookupByLibrary.simpleMessage("سنة"),
        "years": MessageLookupByLibrary.simpleMessage("سنوات"),
        "yearsHosting": MessageLookupByLibrary.simpleMessage("سنوات الاستضافة"),
        "yearsOnAirbnb": MessageLookupByLibrary.simpleMessage("سنة على Airbnb"),
        "yourRights": MessageLookupByLibrary.simpleMessage("حقوقك"),
        "yourRightsDesc": MessageLookupByLibrary.simpleMessage(
            "حقوق الخصوصية الخاصة بك وكيفية ممارستها")
      };
}
