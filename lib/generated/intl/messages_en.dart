// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(price) => "${price} SR";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "about": MessageLookupByLibrary.simpleMessage("About App"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("About App"),
        "aboutThisPlace":
            MessageLookupByLibrary.simpleMessage("About this place"),
        "accessibility": MessageLookupByLibrary.simpleMessage("Accessibility"),
        "accountSettings":
            MessageLookupByLibrary.simpleMessage("Account Settings"),
        "actionParameters":
            MessageLookupByLibrary.simpleMessage("Action Parameters"),
        "activate": MessageLookupByLibrary.simpleMessage("Activate"),
        "activateAll": MessageLookupByLibrary.simpleMessage("Activate All"),
        "activateAllDescription": MessageLookupByLibrary.simpleMessage(
            "Make all selected listings visible to guests"),
        "activateSelected":
            MessageLookupByLibrary.simpleMessage("Activate Selected"),
        "activateSelectedConfirmation": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to activate the selected listings?"),
        "active": MessageLookupByLibrary.simpleMessage("Active"),
        "activeListings":
            MessageLookupByLibrary.simpleMessage("Active Listings"),
        "activeStatusDescription": MessageLookupByLibrary.simpleMessage(
            "Your listing is live and visible to guests"),
        "addImage": MessageLookupByLibrary.simpleMessage("Add Image"),
        "addImages": MessageLookupByLibrary.simpleMessage("Add Images"),
        "addNewListing":
            MessageLookupByLibrary.simpleMessage("Add New Listing"),
        "addProperty": MessageLookupByLibrary.simpleMessage("Add Property"),
        "addToFavorites":
            MessageLookupByLibrary.simpleMessage("Add to favorites"),
        "additionalInfo":
            MessageLookupByLibrary.simpleMessage("Additional Information"),
        "additionalSettings":
            MessageLookupByLibrary.simpleMessage("Additional Settings"),
        "advancedBulkActions":
            MessageLookupByLibrary.simpleMessage("Advanced Bulk Actions"),
        "airConditioning":
            MessageLookupByLibrary.simpleMessage("Air Conditioning"),
        "all": MessageLookupByLibrary.simpleMessage("All"),
        "allCategories": MessageLookupByLibrary.simpleMessage("All Categories"),
        "allListings": MessageLookupByLibrary.simpleMessage("All Listings"),
        "allReviews": MessageLookupByLibrary.simpleMessage("All"),
        "amenities": MessageLookupByLibrary.simpleMessage("Amenities"),
        "analytics": MessageLookupByLibrary.simpleMessage("Analytics"),
        "analyticsOverview":
            MessageLookupByLibrary.simpleMessage("Analytics Overview"),
        "appDescription": MessageLookupByLibrary.simpleMessage(
            "Property booking and rental app"),
        "appInfo":
            MessageLookupByLibrary.simpleMessage("App information and version"),
        "appInformation":
            MessageLookupByLibrary.simpleMessage("App information and version"),
        "appName": MessageLookupByLibrary.simpleMessage("Gather Point"),
        "appearance": MessageLookupByLibrary.simpleMessage("Appearance"),
        "applyAction": MessageLookupByLibrary.simpleMessage("Apply Action"),
        "applyDiscount": MessageLookupByLibrary.simpleMessage("Apply Discount"),
        "applyDiscountDescription":
            MessageLookupByLibrary.simpleMessage("Apply temporary discount"),
        "applyFilter": MessageLookupByLibrary.simpleMessage("Apply Filter"),
        "applyFilters": MessageLookupByLibrary.simpleMessage("Apply"),
        "arabic": MessageLookupByLibrary.simpleMessage("Arabic"),
        "archiveAll": MessageLookupByLibrary.simpleMessage("Archive All"),
        "archiveAllDescription":
            MessageLookupByLibrary.simpleMessage("Archive selected listings"),
        "availableBalance":
            MessageLookupByLibrary.simpleMessage("Available Balance"),
        "availableServices":
            MessageLookupByLibrary.simpleMessage("Available Services"),
        "average": MessageLookupByLibrary.simpleMessage("Average"),
        "averageDailyRate":
            MessageLookupByLibrary.simpleMessage("Average Daily Rate"),
        "averageRating": MessageLookupByLibrary.simpleMessage("Average Rating"),
        "back": MessageLookupByLibrary.simpleMessage("Back"),
        "backToSearch": MessageLookupByLibrary.simpleMessage("Back to Search"),
        "balcony": MessageLookupByLibrary.simpleMessage("Balcony"),
        "bankTransfer": MessageLookupByLibrary.simpleMessage("Bank Transfer"),
        "bar": MessageLookupByLibrary.simpleMessage("Bar"),
        "basicInformation":
            MessageLookupByLibrary.simpleMessage("Basic Information"),
        "bathrooms": MessageLookupByLibrary.simpleMessage("Bathrooms"),
        "beachAccess": MessageLookupByLibrary.simpleMessage("Beach Access"),
        "bedrooms": MessageLookupByLibrary.simpleMessage("Bedrooms"),
        "bio": MessageLookupByLibrary.simpleMessage("Bio"),
        "birthdate": MessageLookupByLibrary.simpleMessage("Birthdate"),
        "bookNow": MessageLookupByLibrary.simpleMessage("Book Now"),
        "bookingDate": MessageLookupByLibrary.simpleMessage("Booking Date"),
        "bookingDetails":
            MessageLookupByLibrary.simpleMessage("Booking Details & Policies"),
        "bookingFee": MessageLookupByLibrary.simpleMessage("Booking fee"),
        "bookingMetrics":
            MessageLookupByLibrary.simpleMessage("Booking Metrics"),
        "bookingPolicy": MessageLookupByLibrary.simpleMessage("Booking Policy"),
        "bookingRules": MessageLookupByLibrary.simpleMessage("Booking Rules"),
        "bookingStatus": MessageLookupByLibrary.simpleMessage("Booking Status"),
        "bookingSummary":
            MessageLookupByLibrary.simpleMessage("Booking Summary"),
        "bookingsChart": MessageLookupByLibrary.simpleMessage("Bookings Chart"),
        "bookingsOverview":
            MessageLookupByLibrary.simpleMessage("Bookings Overview"),
        "browseReels": MessageLookupByLibrary.simpleMessage("Browse Reels"),
        "budgetFriendly":
            MessageLookupByLibrary.simpleMessage("Budget Friendly"),
        "bulkActionCompleted": MessageLookupByLibrary.simpleMessage(
            "Bulk action completed successfully"),
        "bulkActionError": MessageLookupByLibrary.simpleMessage(
            "Error performing bulk action"),
        "bulkActions": MessageLookupByLibrary.simpleMessage("Bulk Actions"),
        "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
        "cancelBooking": MessageLookupByLibrary.simpleMessage("Cancel Booking"),
        "cancelReservation":
            MessageLookupByLibrary.simpleMessage("Cancel Reservation"),
        "cancelSelection":
            MessageLookupByLibrary.simpleMessage("Cancel Selection"),
        "cancellationPolicy":
            MessageLookupByLibrary.simpleMessage("Cancellation Policy"),
        "cancellationPolicyNote": MessageLookupByLibrary.simpleMessage(
            "Remember that the policy set by the host suits your circumstances. In rare cases, you may be eligible for a partial or full refund according to the site\'s policy."),
        "cancellationRate":
            MessageLookupByLibrary.simpleMessage("Cancellation Rate"),
        "cancellationRules":
            MessageLookupByLibrary.simpleMessage("Cancellation Rules"),
        "cancelled": MessageLookupByLibrary.simpleMessage("Cancelled"),
        "cancelledBookings":
            MessageLookupByLibrary.simpleMessage("Cancelled Bookings"),
        "categories": MessageLookupByLibrary.simpleMessage("Categories"),
        "category": MessageLookupByLibrary.simpleMessage("Category"),
        "change": MessageLookupByLibrary.simpleMessage("Change"),
        "changeListingStatus":
            MessageLookupByLibrary.simpleMessage("Change Listing Status"),
        "changeReason": MessageLookupByLibrary.simpleMessage("Change Reason"),
        "changeStatus": MessageLookupByLibrary.simpleMessage("Change Status"),
        "charts": MessageLookupByLibrary.simpleMessage("Charts"),
        "checkConnection": MessageLookupByLibrary.simpleMessage(
            "Check your internet connection"),
        "checkIn": MessageLookupByLibrary.simpleMessage("Check In"),
        "checkInDate": MessageLookupByLibrary.simpleMessage("Check-in Date"),
        "checkInInstructions":
            MessageLookupByLibrary.simpleMessage("Check-in Instructions"),
        "checkInOut": MessageLookupByLibrary.simpleMessage("Check-in/out"),
        "checkInternetConnection": MessageLookupByLibrary.simpleMessage(
            "Please check your internet connection and try again"),
        "checkOut": MessageLookupByLibrary.simpleMessage("Check Out"),
        "checkOutDate": MessageLookupByLibrary.simpleMessage("Check-out Date"),
        "cityView": MessageLookupByLibrary.simpleMessage("City View"),
        "clearFilter": MessageLookupByLibrary.simpleMessage("Clear Filter"),
        "clearFilters": MessageLookupByLibrary.simpleMessage("Clear Filters"),
        "clearSelection":
            MessageLookupByLibrary.simpleMessage("Clear Selection"),
        "codeExpired": MessageLookupByLibrary.simpleMessage("Code expired"),
        "comment": MessageLookupByLibrary.simpleMessage("Comment"),
        "commentFailed":
            MessageLookupByLibrary.simpleMessage("Failed to post comment"),
        "commentPosted":
            MessageLookupByLibrary.simpleMessage("Comment posted successfully"),
        "comments": MessageLookupByLibrary.simpleMessage("Comments"),
        "commission": MessageLookupByLibrary.simpleMessage("Commission"),
        "completed": MessageLookupByLibrary.simpleMessage("Completed"),
        "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
        "confirmBooking":
            MessageLookupByLibrary.simpleMessage("Confirm Booking"),
        "confirmLocation":
            MessageLookupByLibrary.simpleMessage("Confirm Location"),
        "confirmReservation":
            MessageLookupByLibrary.simpleMessage("Confirm Reservation"),
        "confirmSubmission":
            MessageLookupByLibrary.simpleMessage("Confirm Submission"),
        "confirmSubmissionMessage": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to submit this property?"),
        "confirmed": MessageLookupByLibrary.simpleMessage("Confirmed"),
        "confirmedBookings":
            MessageLookupByLibrary.simpleMessage("Confirmed Bookings"),
        "connectionError":
            MessageLookupByLibrary.simpleMessage("Connection Error"),
        "contactHost": MessageLookupByLibrary.simpleMessage("Contact Host"),
        "contactInfo":
            MessageLookupByLibrary.simpleMessage("Contact Information"),
        "contactSupport":
            MessageLookupByLibrary.simpleMessage("Contact Support"),
        "contactUs": MessageLookupByLibrary.simpleMessage("Contact Us"),
        "contactUsDesc": MessageLookupByLibrary.simpleMessage(
            "Get in touch with our support team"),
        "continueAsGuest":
            MessageLookupByLibrary.simpleMessage("Continue as Guest"),
        "continueButton": MessageLookupByLibrary.simpleMessage("Continue"),
        "conversionRate":
            MessageLookupByLibrary.simpleMessage("Conversion Rate"),
        "convertToDraft":
            MessageLookupByLibrary.simpleMessage("Convert to Draft"),
        "convertToDraftDescription": MessageLookupByLibrary.simpleMessage(
            "Convert all selected listings to draft status"),
        "createFirstListing":
            MessageLookupByLibrary.simpleMessage("Create Your First Listing"),
        "createProperty":
            MessageLookupByLibrary.simpleMessage("Create Property"),
        "currencyCode": MessageLookupByLibrary.simpleMessage("SAR"),
        "currencySymbol": MessageLookupByLibrary.simpleMessage("SR"),
        "currentLocation":
            MessageLookupByLibrary.simpleMessage("Current Location"),
        "currentStatus": MessageLookupByLibrary.simpleMessage("Current Status"),
        "currentStatusBreakdown":
            MessageLookupByLibrary.simpleMessage("Current Status Breakdown"),
        "customizeExperience": MessageLookupByLibrary.simpleMessage(
            "Customize your app experience"),
        "dailyBookings": MessageLookupByLibrary.simpleMessage("Daily Bookings"),
        "dailyPrice": MessageLookupByLibrary.simpleMessage("Daily Price"),
        "dailyRevenue": MessageLookupByLibrary.simpleMessage("Daily Revenue"),
        "dailyViews": MessageLookupByLibrary.simpleMessage("Daily Views"),
        "darkMode": MessageLookupByLibrary.simpleMessage("Dark Mode"),
        "dashboardOverview":
            MessageLookupByLibrary.simpleMessage("Dashboard Overview"),
        "dataAndPrivacy":
            MessageLookupByLibrary.simpleMessage("Data & Privacy"),
        "dataCollection":
            MessageLookupByLibrary.simpleMessage("Data Collection"),
        "dataCollectionDesc": MessageLookupByLibrary.simpleMessage(
            "How we collect and use your data"),
        "dataLoadError":
            MessageLookupByLibrary.simpleMessage("Error loading data"),
        "dataLoadFailed":
            MessageLookupByLibrary.simpleMessage("Failed to load data"),
        "dataRetention": MessageLookupByLibrary.simpleMessage("Data Retention"),
        "dataRetentionDesc":
            MessageLookupByLibrary.simpleMessage("How long we keep your data"),
        "dates": MessageLookupByLibrary.simpleMessage("Dates"),
        "days": MessageLookupByLibrary.simpleMessage("Days"),
        "deactivate": MessageLookupByLibrary.simpleMessage("Deactivate"),
        "deactivateAll": MessageLookupByLibrary.simpleMessage("Deactivate All"),
        "deactivateAllDescription": MessageLookupByLibrary.simpleMessage(
            "Hide all selected listings from guests"),
        "deactivateListing":
            MessageLookupByLibrary.simpleMessage("Deactivate Listing"),
        "deactivateListingConfirmation": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to deactivate this listing?"),
        "deactivateSelected":
            MessageLookupByLibrary.simpleMessage("Deactivate Selected"),
        "deactivateSelectedConfirmation": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to deactivate the selected listings?"),
        "decreasePrices":
            MessageLookupByLibrary.simpleMessage("Decrease Prices"),
        "decreasePricesDescription": MessageLookupByLibrary.simpleMessage(
            "Decrease prices by percentage"),
        "deleteAccount": MessageLookupByLibrary.simpleMessage("Delete Account"),
        "deleteAccountDesc": MessageLookupByLibrary.simpleMessage(
            "Permanently delete your account and data"),
        "deleteAll": MessageLookupByLibrary.simpleMessage("Delete All"),
        "deleteAllDescription": MessageLookupByLibrary.simpleMessage(
            "Permanently delete all selected listings"),
        "deleteComment": MessageLookupByLibrary.simpleMessage("Delete Comment"),
        "deleteSelected":
            MessageLookupByLibrary.simpleMessage("Delete Selected"),
        "deleteSelectedConfirmation": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to delete the selected listings? This action cannot be undone."),
        "description": MessageLookupByLibrary.simpleMessage("Description"),
        "details": MessageLookupByLibrary.simpleMessage("Details"),
        "detectingLocation":
            MessageLookupByLibrary.simpleMessage("Detecting location..."),
        "didntReceiveCode":
            MessageLookupByLibrary.simpleMessage("Didn\'t receive the code?"),
        "discountDuration":
            MessageLookupByLibrary.simpleMessage("Discount Duration"),
        "discountPercentage":
            MessageLookupByLibrary.simpleMessage("Discount Percentage"),
        "discoverLatestVisualContent": MessageLookupByLibrary.simpleMessage(
            "Discover Latest Visual Content"),
        "discoverMore": MessageLookupByLibrary.simpleMessage("Discover More"),
        "downloadReceipt":
            MessageLookupByLibrary.simpleMessage("Download Receipt"),
        "draftStatusDescription": MessageLookupByLibrary.simpleMessage(
            "Your listing is saved but not published yet"),
        "drafts": MessageLookupByLibrary.simpleMessage("Drafts"),
        "duplicateAll": MessageLookupByLibrary.simpleMessage("Duplicate All"),
        "duplicateAllDescription": MessageLookupByLibrary.simpleMessage(
            "Create copies of selected listings"),
        "earnings": MessageLookupByLibrary.simpleMessage("Earnings"),
        "earningsChart": MessageLookupByLibrary.simpleMessage("Earnings Chart"),
        "earningsOverview":
            MessageLookupByLibrary.simpleMessage("Earnings Overview"),
        "editComment": MessageLookupByLibrary.simpleMessage("Edit Comment"),
        "editListing": MessageLookupByLibrary.simpleMessage("Edit Listing"),
        "editProfile": MessageLookupByLibrary.simpleMessage("Edit Profile"),
        "editProperty": MessageLookupByLibrary.simpleMessage("Edit Property"),
        "editWhilePending":
            MessageLookupByLibrary.simpleMessage("Edit While Pending"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "enableAllNotifications": MessageLookupByLibrary.simpleMessage(
            "Enable or disable all notifications"),
        "enableHostMode": MessageLookupByLibrary.simpleMessage(
            "Enable host mode to manage your properties"),
        "enableNotificationsInSettings": MessageLookupByLibrary.simpleMessage(
            "Please enable notifications in device settings"),
        "engagementMetrics":
            MessageLookupByLibrary.simpleMessage("Engagement Metrics"),
        "english": MessageLookupByLibrary.simpleMessage("English"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Enter Amount"),
        "enterBookingRules":
            MessageLookupByLibrary.simpleMessage("Enter booking rules"),
        "enterCancellationRules":
            MessageLookupByLibrary.simpleMessage("Enter cancellation rules"),
        "enterChangeReason": MessageLookupByLibrary.simpleMessage(
            "Enter reason for status change..."),
        "enterPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Enter your phone number"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Enter price"),
        "enterPropertyDescription":
            MessageLookupByLibrary.simpleMessage("Enter property description"),
        "enterPropertyTitle":
            MessageLookupByLibrary.simpleMessage("Enter property title"),
        "enterSearchTerm":
            MessageLookupByLibrary.simpleMessage("Enter search term..."),
        "enterVerificationCode":
            MessageLookupByLibrary.simpleMessage("Enter verification code"),
        "error": MessageLookupByLibrary.simpleMessage("Error"),
        "errorFetchingInfo": MessageLookupByLibrary.simpleMessage(
            "An error occurred while fetching information"),
        "errorLoadingListings":
            MessageLookupByLibrary.simpleMessage("Error Loading Listings"),
        "errorPersistsContact": MessageLookupByLibrary.simpleMessage(
            "If the error persists, please contact support"),
        "eventNotifications":
            MessageLookupByLibrary.simpleMessage("Event Notifications"),
        "excellent": MessageLookupByLibrary.simpleMessage("Excellent"),
        "executeAction": MessageLookupByLibrary.simpleMessage("Execute Action"),
        "exploreAllCategoriesSubtitle": MessageLookupByLibrary.simpleMessage(
            "Explore all available categories"),
        "exploreCategories":
            MessageLookupByLibrary.simpleMessage("Explore Categories"),
        "exploreProperties":
            MessageLookupByLibrary.simpleMessage("Explore Properties"),
        "exportAll": MessageLookupByLibrary.simpleMessage("Export All"),
        "exportAllDescription":
            MessageLookupByLibrary.simpleMessage("Export listing data"),
        "exportData": MessageLookupByLibrary.simpleMessage("Export Data"),
        "facilities": MessageLookupByLibrary.simpleMessage("Facilities"),
        "failedToCreateItem":
            MessageLookupByLibrary.simpleMessage("Failed to create item"),
        "failedToLoadCategories":
            MessageLookupByLibrary.simpleMessage("Failed to load categories"),
        "failedToLoadFacilities":
            MessageLookupByLibrary.simpleMessage("Failed to load facilities"),
        "failedToLoadReels":
            MessageLookupByLibrary.simpleMessage("Failed to load reels"),
        "failedToLoadVideo":
            MessageLookupByLibrary.simpleMessage("Failed to load video"),
        "fair": MessageLookupByLibrary.simpleMessage("Fair"),
        "familyFriendly":
            MessageLookupByLibrary.simpleMessage("Family Friendly"),
        "favoriteCount": MessageLookupByLibrary.simpleMessage("Favorites"),
        "featureRequiresLogin": MessageLookupByLibrary.simpleMessage(
            "This feature requires you to login. Please create an account or login to access this feature."),
        "featureUnavailable":
            MessageLookupByLibrary.simpleMessage("Feature Unavailable"),
        "featuredPlaces":
            MessageLookupByLibrary.simpleMessage("Featured Places"),
        "female": MessageLookupByLibrary.simpleMessage("Female"),
        "filter": MessageLookupByLibrary.simpleMessage("Filter"),
        "filterReels": MessageLookupByLibrary.simpleMessage("Filter Reels"),
        "filterResults": MessageLookupByLibrary.simpleMessage("Filter Results"),
        "finalPrice": MessageLookupByLibrary.simpleMessage("Final Price"),
        "flexiblePolicy": MessageLookupByLibrary.simpleMessage("Flexible"),
        "foundHelpful": MessageLookupByLibrary.simpleMessage(
            "people found this review helpful"),
        "freeParking": MessageLookupByLibrary.simpleMessage("Free Parking"),
        "freeWifi": MessageLookupByLibrary.simpleMessage("Free WiFi"),
        "freeWifiArabic": MessageLookupByLibrary.simpleMessage("Free WiFi"),
        "fullName": MessageLookupByLibrary.simpleMessage("Full Name"),
        "gallery": MessageLookupByLibrary.simpleMessage("Gallery"),
        "garden": MessageLookupByLibrary.simpleMessage("Garden"),
        "gardenView": MessageLookupByLibrary.simpleMessage("Garden View"),
        "gatherPoint": MessageLookupByLibrary.simpleMessage("Gather Point"),
        "gender": MessageLookupByLibrary.simpleMessage("Gender"),
        "good": MessageLookupByLibrary.simpleMessage("Good"),
        "gridView": MessageLookupByLibrary.simpleMessage("Grid View"),
        "guest": MessageLookupByLibrary.simpleMessage("Guest"),
        "guestComment": MessageLookupByLibrary.simpleMessage("Guest Comment"),
        "guestFavorite": MessageLookupByLibrary.simpleMessage("Guest Favorite"),
        "guestLimitations":
            MessageLookupByLibrary.simpleMessage("Guest Limitations:"),
        "guestLimitationsDetails": MessageLookupByLibrary.simpleMessage(
            "• Cannot save favorites\n• Cannot write reviews\n• Limited reservation history\n• No profile management"),
        "guestModeInfo": MessageLookupByLibrary.simpleMessage(
            "As a guest, you can browse and make reservations, but some features like favorites and reviews require an account."),
        "guestName": MessageLookupByLibrary.simpleMessage("Guest Name"),
        "guestRating": MessageLookupByLibrary.simpleMessage("Guest review"),
        "guestReservation":
            MessageLookupByLibrary.simpleMessage("Guest Reservation"),
        "guestReservationMessage": MessageLookupByLibrary.simpleMessage(
            "You are currently browsing as a guest. You can proceed with the reservation, but creating an account will give you access to more features."),
        "guestReview": MessageLookupByLibrary.simpleMessage("Guest Review"),
        "guests": MessageLookupByLibrary.simpleMessage("Guests"),
        "gym": MessageLookupByLibrary.simpleMessage("Gym"),
        "heating": MessageLookupByLibrary.simpleMessage("Heating"),
        "hideComments": MessageLookupByLibrary.simpleMessage("Hide Comments"),
        "highRated": MessageLookupByLibrary.simpleMessage("High Rated"),
        "home": MessageLookupByLibrary.simpleMessage("Home"),
        "hostDashboard": MessageLookupByLibrary.simpleMessage("Host Dashboard"),
        "hostMode": MessageLookupByLibrary.simpleMessage("Host Mode"),
        "hostModeDescription": MessageLookupByLibrary.simpleMessage(
            "Enable host mode to manage your properties"),
        "hostName": MessageLookupByLibrary.simpleMessage("Host Name"),
        "hostedBy": MessageLookupByLibrary.simpleMessage("Hosted by"),
        "hostingTips": MessageLookupByLibrary.simpleMessage("Hosting Tips"),
        "houseRules": MessageLookupByLibrary.simpleMessage("House Rules"),
        "imageGallery": MessageLookupByLibrary.simpleMessage("Image Gallery"),
        "importantInfo":
            MessageLookupByLibrary.simpleMessage("Important Information"),
        "inHosting": MessageLookupByLibrary.simpleMessage("hosting"),
        "inactive": MessageLookupByLibrary.simpleMessage("Inactive"),
        "inactiveListings":
            MessageLookupByLibrary.simpleMessage("Inactive Listings"),
        "inactiveStatusDescription": MessageLookupByLibrary.simpleMessage(
            "Your listing is hidden from guests"),
        "increasePrices":
            MessageLookupByLibrary.simpleMessage("Increase Prices"),
        "increasePricesDescription": MessageLookupByLibrary.simpleMessage(
            "Increase prices by percentage"),
        "insights": MessageLookupByLibrary.simpleMessage("Insights"),
        "instantBook": MessageLookupByLibrary.simpleMessage("Instant Book"),
        "invalidCode": MessageLookupByLibrary.simpleMessage("Invalid code"),
        "invalidDate": MessageLookupByLibrary.simpleMessage("Invalid date"),
        "invalidPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Invalid phone number"),
        "itemsSelected": MessageLookupByLibrary.simpleMessage("items selected"),
        "joinAsHost": MessageLookupByLibrary.simpleMessage("Join as Host"),
        "joinAsHostSubtitle": MessageLookupByLibrary.simpleMessage(
            "It\'s easy to start hosting and earn extra income"),
        "kitchen": MessageLookupByLibrary.simpleMessage("Kitchen"),
        "knowledge": MessageLookupByLibrary.simpleMessage("Knowledge"),
        "lakeView": MessageLookupByLibrary.simpleMessage("Lake View"),
        "language": MessageLookupByLibrary.simpleMessage("Language"),
        "last30Days": MessageLookupByLibrary.simpleMessage("Last 30 Days"),
        "last6Months": MessageLookupByLibrary.simpleMessage("Last 6 Months"),
        "last7Days": MessageLookupByLibrary.simpleMessage("Last 7 Days"),
        "last90Days": MessageLookupByLibrary.simpleMessage("Last 90 Days"),
        "lastMonth": MessageLookupByLibrary.simpleMessage("Last Month"),
        "lastYear": MessageLookupByLibrary.simpleMessage("Last Year"),
        "latitude": MessageLookupByLibrary.simpleMessage("Latitude"),
        "laundry": MessageLookupByLibrary.simpleMessage("Laundry"),
        "leaveReview": MessageLookupByLibrary.simpleMessage("Leave Review"),
        "legal": MessageLookupByLibrary.simpleMessage("Legal"),
        "listView": MessageLookupByLibrary.simpleMessage("List View"),
        "listingStatus": MessageLookupByLibrary.simpleMessage("Listing Status"),
        "listingsSelected":
            MessageLookupByLibrary.simpleMessage("listings selected"),
        "listingsWillBeAffected":
            MessageLookupByLibrary.simpleMessage("listings will be affected"),
        "loading": MessageLookupByLibrary.simpleMessage("Loading..."),
        "loadingData": MessageLookupByLibrary.simpleMessage("Loading data..."),
        "loadingReels":
            MessageLookupByLibrary.simpleMessage("Loading reels..."),
        "location": MessageLookupByLibrary.simpleMessage("Location"),
        "locationPermissionError": MessageLookupByLibrary.simpleMessage(
            "Please enable location permission to use the app"),
        "login": MessageLookupByLibrary.simpleMessage("Login"),
        "loginForBetterExperience":
            MessageLookupByLibrary.simpleMessage("Login for Better Experience"),
        "loginRequiredForFavorites": MessageLookupByLibrary.simpleMessage(
            "You need to login to add items to your favorites list."),
        "loginRequiredForReservation": MessageLookupByLibrary.simpleMessage(
            "You need to login to make a reservation. Guest users can also make reservations with limited features."),
        "loginRequiredForReviews": MessageLookupByLibrary.simpleMessage(
            "You need to login to write reviews and share your experience."),
        "logout": MessageLookupByLibrary.simpleMessage("Logout"),
        "logoutConfirmation": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to logout?"),
        "longTermBookings": MessageLookupByLibrary.simpleMessage(
            "Long-term bookings (>28 days)"),
        "longitude": MessageLookupByLibrary.simpleMessage("Longitude"),
        "luxuryStay": MessageLookupByLibrary.simpleMessage("Luxury Stay"),
        "mainImage": MessageLookupByLibrary.simpleMessage("Main Image"),
        "male": MessageLookupByLibrary.simpleMessage("Male"),
        "manageNotifications": MessageLookupByLibrary.simpleMessage(
            "Manage notification settings"),
        "managementActions":
            MessageLookupByLibrary.simpleMessage("Management Actions"),
        "mapView": MessageLookupByLibrary.simpleMessage("Map view"),
        "march2024": MessageLookupByLibrary.simpleMessage("March 2024"),
        "marketingNotifications":
            MessageLookupByLibrary.simpleMessage("Marketing Notifications"),
        "maxGuests": MessageLookupByLibrary.simpleMessage("Max Guests"),
        "media": MessageLookupByLibrary.simpleMessage("Media"),
        "messageNotifications":
            MessageLookupByLibrary.simpleMessage("Message Notifications"),
        "minimumRating": MessageLookupByLibrary.simpleMessage("Minimum Rating"),
        "minimumWithdraw":
            MessageLookupByLibrary.simpleMessage("Minimum withdrawal: SR 50"),
        "minimumWithdrawAmount":
            MessageLookupByLibrary.simpleMessage("Minimum withdrawal: SR 50"),
        "moderatePolicy": MessageLookupByLibrary.simpleMessage("Moderate"),
        "modifyReservation":
            MessageLookupByLibrary.simpleMessage("Modify Reservation"),
        "monthlyPrice": MessageLookupByLibrary.simpleMessage("Monthly Price"),
        "moreActions": MessageLookupByLibrary.simpleMessage("More Actions"),
        "mostCommented": MessageLookupByLibrary.simpleMessage("Most Commented"),
        "mostLiked": MessageLookupByLibrary.simpleMessage("Most Liked"),
        "mountainView": MessageLookupByLibrary.simpleMessage("Mountain View"),
        "mustLogin": MessageLookupByLibrary.simpleMessage("You must login"),
        "mustLoginDescription": MessageLookupByLibrary.simpleMessage(
            "Please login to view your profile"),
        "muteVideo": MessageLookupByLibrary.simpleMessage("Mute Video"),
        "myBookings": MessageLookupByLibrary.simpleMessage("My Bookings"),
        "myListings": MessageLookupByLibrary.simpleMessage("My Listings"),
        "nearbyAttractions":
            MessageLookupByLibrary.simpleMessage("Nearby Attractions"),
        "nearbyPlaces": MessageLookupByLibrary.simpleMessage("Nearby Places"),
        "needHelp": MessageLookupByLibrary.simpleMessage("Need help?"),
        "needsAttention":
            MessageLookupByLibrary.simpleMessage("Needs Attention"),
        "netRevenue": MessageLookupByLibrary.simpleMessage("Net Revenue"),
        "newEventsAndUpdates": MessageLookupByLibrary.simpleMessage(
            "Notifications about new events and updates"),
        "newHost": MessageLookupByLibrary.simpleMessage("New host"),
        "newListing": MessageLookupByLibrary.simpleMessage("New Listing"),
        "newMessagesAndChats": MessageLookupByLibrary.simpleMessage(
            "Notifications for new messages and conversations"),
        "newPrice": MessageLookupByLibrary.simpleMessage("New Price"),
        "newest": MessageLookupByLibrary.simpleMessage("Newest"),
        "next": MessageLookupByLibrary.simpleMessage("Next"),
        "night": MessageLookupByLibrary.simpleMessage("night"),
        "nights": MessageLookupByLibrary.simpleMessage("Nights"),
        "nightsStayed": MessageLookupByLibrary.simpleMessage("Nights Stayed"),
        "noAmenitiesListed":
            MessageLookupByLibrary.simpleMessage("No amenities listed"),
        "noBookingsMessage":
            MessageLookupByLibrary.simpleMessage("No recent bookings"),
        "noBookingsSubtitle": MessageLookupByLibrary.simpleMessage(
            "You haven\'t made any bookings yet"),
        "noBookingsYet":
            MessageLookupByLibrary.simpleMessage("No bookings yet"),
        "noComments": MessageLookupByLibrary.simpleMessage("No comments yet"),
        "noData": MessageLookupByLibrary.simpleMessage("No data available"),
        "noDataAvailable":
            MessageLookupByLibrary.simpleMessage("No data available"),
        "noDescription": MessageLookupByLibrary.simpleMessage("No description"),
        "noDescriptionAvailable":
            MessageLookupByLibrary.simpleMessage("No description available."),
        "noInternetConnection":
            MessageLookupByLibrary.simpleMessage("No Internet Connection"),
        "noListingsDescription": MessageLookupByLibrary.simpleMessage(
            "Start your hosting journey by creating your first property listing. Share your space with travelers and start earning!"),
        "noListingsYet":
            MessageLookupByLibrary.simpleMessage("No Listings Yet"),
        "noPropertiesSubtitle": MessageLookupByLibrary.simpleMessage(
            "Start by adding your first property"),
        "noPropertiesYet":
            MessageLookupByLibrary.simpleMessage("No properties yet"),
        "noRecentBookings":
            MessageLookupByLibrary.simpleMessage("No recent bookings"),
        "noRecentReviews":
            MessageLookupByLibrary.simpleMessage("No recent reviews"),
        "noResults": MessageLookupByLibrary.simpleMessage("No results found"),
        "noResultsFound":
            MessageLookupByLibrary.simpleMessage("No results found"),
        "noReviewsFound":
            MessageLookupByLibrary.simpleMessage("No reviews found"),
        "noReviewsMatchFilter": MessageLookupByLibrary.simpleMessage(
            "No reviews match the selected filter"),
        "noReviewsMessage":
            MessageLookupByLibrary.simpleMessage("No recent reviews"),
        "noSearchResults":
            MessageLookupByLibrary.simpleMessage("No search results"),
        "noSmoking": MessageLookupByLibrary.simpleMessage("No Smoking"),
        "noTitle": MessageLookupByLibrary.simpleMessage("No title"),
        "noView": MessageLookupByLibrary.simpleMessage("No View"),
        "normalDays": MessageLookupByLibrary.simpleMessage("Normal Days"),
        "notAvailable": MessageLookupByLibrary.simpleMessage("Not available"),
        "notSpecified": MessageLookupByLibrary.simpleMessage("Not Specified"),
        "notificationPermissionRequired": MessageLookupByLibrary.simpleMessage(
            "Notification Permission Required"),
        "notificationSettings":
            MessageLookupByLibrary.simpleMessage("Notification Settings"),
        "notifications": MessageLookupByLibrary.simpleMessage("Notifications"),
        "numberOfBathrooms":
            MessageLookupByLibrary.simpleMessage("Number of Bathrooms"),
        "numberOfBedrooms":
            MessageLookupByLibrary.simpleMessage("Number of Bedrooms"),
        "numberOfDays": MessageLookupByLibrary.simpleMessage("Number of Days"),
        "numberOfGuests":
            MessageLookupByLibrary.simpleMessage("Number of Guests"),
        "occupancyRate": MessageLookupByLibrary.simpleMessage("Occupancy Rate"),
        "oceanView": MessageLookupByLibrary.simpleMessage("Ocean View"),
        "ofPreposition": MessageLookupByLibrary.simpleMessage("of"),
        "offersAndMarketing": MessageLookupByLibrary.simpleMessage(
            "Notifications about offers and marketing news"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "oldest": MessageLookupByLibrary.simpleMessage("Oldest"),
        "openSettings": MessageLookupByLibrary.simpleMessage("Open Settings"),
        "or": MessageLookupByLibrary.simpleMessage("OR"),
        "overview": MessageLookupByLibrary.simpleMessage("Overview"),
        "partyFriendly": MessageLookupByLibrary.simpleMessage("Party Friendly"),
        "pauseVideo": MessageLookupByLibrary.simpleMessage("Pause Video"),
        "paypal": MessageLookupByLibrary.simpleMessage("PayPal"),
        "pending": MessageLookupByLibrary.simpleMessage("Pending"),
        "pendingEarnings":
            MessageLookupByLibrary.simpleMessage("Pending Earnings"),
        "pendingReservations":
            MessageLookupByLibrary.simpleMessage("Pending Reservations"),
        "pendingReview": MessageLookupByLibrary.simpleMessage("Pending Review"),
        "pendingStatusDescription": MessageLookupByLibrary.simpleMessage(
            "Your listing is under review by our team"),
        "perNight": MessageLookupByLibrary.simpleMessage("SR / night"),
        "percentage": MessageLookupByLibrary.simpleMessage("Percentage"),
        "performanceGrade":
            MessageLookupByLibrary.simpleMessage("Performance Grade"),
        "personalInfo":
            MessageLookupByLibrary.simpleMessage("Personal Information"),
        "petFriendly": MessageLookupByLibrary.simpleMessage("Pet Friendly"),
        "phone": MessageLookupByLibrary.simpleMessage("Phone"),
        "phoneNumberHint": MessageLookupByLibrary.simpleMessage("5xxxxxxxx"),
        "phoneNumberRequired":
            MessageLookupByLibrary.simpleMessage("Phone number is required"),
        "pickLocation": MessageLookupByLibrary.simpleMessage("Pick Location"),
        "playVideo": MessageLookupByLibrary.simpleMessage("Play Video"),
        "pleaseBathrooms":
            MessageLookupByLibrary.simpleMessage("Please enter bathrooms"),
        "pleaseCheckPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Please check phone number"),
        "pleaseEnterBedrooms":
            MessageLookupByLibrary.simpleMessage("Please enter bedrooms"),
        "pleaseEnterDescription":
            MessageLookupByLibrary.simpleMessage("Please enter description"),
        "pleaseEnterMaxGuests":
            MessageLookupByLibrary.simpleMessage("Please enter max guests"),
        "pleaseEnterPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Please enter phone number"),
        "pleaseEnterPrice":
            MessageLookupByLibrary.simpleMessage("Please enter price"),
        "pleaseEnterPropertyTitle":
            MessageLookupByLibrary.simpleMessage("Please enter property title"),
        "pleaseEnterValidNumber":
            MessageLookupByLibrary.simpleMessage("Please enter valid number"),
        "pleaseEnterValidPrice":
            MessageLookupByLibrary.simpleMessage("Please enter valid price"),
        "pleaseSelectBothDates":
            MessageLookupByLibrary.simpleMessage("Please select both dates"),
        "pleaseSelectCancellationPolicy": MessageLookupByLibrary.simpleMessage(
            "Please select cancellation policy"),
        "pleaseSelectCategory":
            MessageLookupByLibrary.simpleMessage("Please select category"),
        "pleaseSelectPropertyType":
            MessageLookupByLibrary.simpleMessage("Please select property type"),
        "policies": MessageLookupByLibrary.simpleMessage("Policies"),
        "policyDescription":
            MessageLookupByLibrary.simpleMessage("Policy Description"),
        "pool": MessageLookupByLibrary.simpleMessage("Pool"),
        "poor": MessageLookupByLibrary.simpleMessage("Poor"),
        "popular": MessageLookupByLibrary.simpleMessage("Popular"),
        "popularDestinations":
            MessageLookupByLibrary.simpleMessage("Popular Destinations"),
        "popularPlaces": MessageLookupByLibrary.simpleMessage("Popular Places"),
        "postComment": MessageLookupByLibrary.simpleMessage("Post Comment"),
        "previousTrip": MessageLookupByLibrary.simpleMessage("Previous Trip"),
        "previousTrips": MessageLookupByLibrary.simpleMessage("Previous Trips"),
        "price": MessageLookupByLibrary.simpleMessage("Price"),
        "priceBredown": MessageLookupByLibrary.simpleMessage("Price Breakdown"),
        "priceDetails": MessageLookupByLibrary.simpleMessage("Price Details"),
        "priceHighToLow":
            MessageLookupByLibrary.simpleMessage("Price: High to Low"),
        "priceLowToHigh":
            MessageLookupByLibrary.simpleMessage("Price: Low to High"),
        "pricePerNight":
            MessageLookupByLibrary.simpleMessage("Price per night"),
        "priceRange": MessageLookupByLibrary.simpleMessage("Price Range"),
        "priceType": MessageLookupByLibrary.simpleMessage("Price Type"),
        "priceWithCurrency": m0,
        "pricing": MessageLookupByLibrary.simpleMessage("Pricing"),
        "pricingActions":
            MessageLookupByLibrary.simpleMessage("Pricing Actions"),
        "privacy": MessageLookupByLibrary.simpleMessage("Privacy & Security"),
        "privacyAndSecurity": MessageLookupByLibrary.simpleMessage(
            "Privacy and security settings"),
        "privacySettings": MessageLookupByLibrary.simpleMessage(
            "Privacy and security settings"),
        "proceedAsGuest":
            MessageLookupByLibrary.simpleMessage("Continue as Guest"),
        "proceedLabel": MessageLookupByLibrary.simpleMessage("Continue"),
        "proceedWithApple":
            MessageLookupByLibrary.simpleMessage("Continue with Apple"),
        "proceedWithGoogle":
            MessageLookupByLibrary.simpleMessage("Continue with Google"),
        "proceedWithPhone": MessageLookupByLibrary.simpleMessage(
            "Continue with your phone number"),
        "processing": MessageLookupByLibrary.simpleMessage("Processing"),
        "profile": MessageLookupByLibrary.simpleMessage("Profile"),
        "profileImage": MessageLookupByLibrary.simpleMessage("Profile Image"),
        "properties": MessageLookupByLibrary.simpleMessage("Properties"),
        "propertyCreatedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Property created successfully!"),
        "propertyDescription":
            MessageLookupByLibrary.simpleMessage("Property Description"),
        "propertyDetails":
            MessageLookupByLibrary.simpleMessage("Property Details"),
        "propertyLocation":
            MessageLookupByLibrary.simpleMessage("Property Location"),
        "propertyName": MessageLookupByLibrary.simpleMessage("Property Name"),
        "propertyPreview":
            MessageLookupByLibrary.simpleMessage("Property Preview"),
        "propertyTitle": MessageLookupByLibrary.simpleMessage("Property Title"),
        "propertyType": MessageLookupByLibrary.simpleMessage("Property Type"),
        "publishAll": MessageLookupByLibrary.simpleMessage("Publish All"),
        "publishAllDescription": MessageLookupByLibrary.simpleMessage(
            "Publish all selected listings"),
        "publishListing":
            MessageLookupByLibrary.simpleMessage("Publish Listing"),
        "publishListingConfirmation": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to publish this listing?"),
        "publishProperty":
            MessageLookupByLibrary.simpleMessage("Publish Property"),
        "pullToRefresh":
            MessageLookupByLibrary.simpleMessage("Pull to refresh"),
        "pushNotifications":
            MessageLookupByLibrary.simpleMessage("Push Notifications"),
        "rareFind": MessageLookupByLibrary.simpleMessage("Rare Find"),
        "rating": MessageLookupByLibrary.simpleMessage("Rating"),
        "ratingHighToLow":
            MessageLookupByLibrary.simpleMessage("Rating: High to Low"),
        "ratingLowToHigh":
            MessageLookupByLibrary.simpleMessage("Rating: Low to High"),
        "rebookProperty":
            MessageLookupByLibrary.simpleMessage("Rebook Property"),
        "recentBookings":
            MessageLookupByLibrary.simpleMessage("Recent Bookings"),
        "recentReviews": MessageLookupByLibrary.simpleMessage("Recent Reviews"),
        "reels": MessageLookupByLibrary.simpleMessage("Reels"),
        "referHost": MessageLookupByLibrary.simpleMessage("Refer Host"),
        "refreshData": MessageLookupByLibrary.simpleMessage("Refresh Data"),
        "rejectionReason":
            MessageLookupByLibrary.simpleMessage("Rejection Reason"),
        "removeFromFavorites":
            MessageLookupByLibrary.simpleMessage("Remove from Favorites"),
        "replyToComment":
            MessageLookupByLibrary.simpleMessage("Reply to Comment"),
        "requestHelp": MessageLookupByLibrary.simpleMessage("Request Help"),
        "resendCode": MessageLookupByLibrary.simpleMessage("Resend code"),
        "reservationConfirmed": MessageLookupByLibrary.simpleMessage(
            "Reservation confirmed successfully!"),
        "reservationFailed": MessageLookupByLibrary.simpleMessage(
            "Failed to confirm reservation"),
        "reservationFrom":
            MessageLookupByLibrary.simpleMessage("Reservation From"),
        "reservationTo": MessageLookupByLibrary.simpleMessage("Reservation To"),
        "reservations": MessageLookupByLibrary.simpleMessage("Reservations"),
        "reserve": MessageLookupByLibrary.simpleMessage("Reserve"),
        "resetFilters": MessageLookupByLibrary.simpleMessage("Reset"),
        "responseRate": MessageLookupByLibrary.simpleMessage("Response Rate"),
        "responseTime": MessageLookupByLibrary.simpleMessage("Response Time"),
        "restaurant": MessageLookupByLibrary.simpleMessage("Restaurant"),
        "retry": MessageLookupByLibrary.simpleMessage("Retry"),
        "retryConnection":
            MessageLookupByLibrary.simpleMessage("Retry Connection"),
        "revenueMetrics":
            MessageLookupByLibrary.simpleMessage("Revenue Metrics"),
        "reviewReservation":
            MessageLookupByLibrary.simpleMessage("Review Reservation"),
        "reviewSubmittedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Review submitted successfully"),
        "reviews": MessageLookupByLibrary.simpleMessage("Reviews"),
        "reviewsCount": MessageLookupByLibrary.simpleMessage("review"),
        "reviewsOverview":
            MessageLookupByLibrary.simpleMessage("Reviews Overview"),
        "rooms": MessageLookupByLibrary.simpleMessage("Rooms"),
        "rules": MessageLookupByLibrary.simpleMessage("Rules"),
        "safetyFeatures":
            MessageLookupByLibrary.simpleMessage("Safety Features"),
        "sampleReviewText": MessageLookupByLibrary.simpleMessage(
            "Great place to stay! Clean and comfortable and exactly as described. The host was very responsive and helpful."),
        "sar": MessageLookupByLibrary.simpleMessage("SR"),
        "sarPerNight": MessageLookupByLibrary.simpleMessage("SR/night"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("Save Changes"),
        "search": MessageLookupByLibrary.simpleMessage("Search"),
        "searchHint": MessageLookupByLibrary.simpleMessage(
            "Search your favorite destination..."),
        "searchListings":
            MessageLookupByLibrary.simpleMessage("Search listings..."),
        "searchPlaceholder": MessageLookupByLibrary.simpleMessage(
            "Welcome... search for what you want"),
        "searchReels": MessageLookupByLibrary.simpleMessage("Search Reels"),
        "searchResults": MessageLookupByLibrary.simpleMessage("Search Results"),
        "searching": MessageLookupByLibrary.simpleMessage("Searching..."),
        "seeAll": MessageLookupByLibrary.simpleMessage("See All"),
        "selectAction": MessageLookupByLibrary.simpleMessage("Select Action"),
        "selectActionCategory":
            MessageLookupByLibrary.simpleMessage("Select Action Category"),
        "selectAll": MessageLookupByLibrary.simpleMessage("Select All"),
        "selectBirthdate":
            MessageLookupByLibrary.simpleMessage("Select Birthdate"),
        "selectCancellationPolicy":
            MessageLookupByLibrary.simpleMessage("Select Cancellation Policy"),
        "selectCategory":
            MessageLookupByLibrary.simpleMessage("Select Category"),
        "selectCity": MessageLookupByLibrary.simpleMessage("Select City"),
        "selectLocation":
            MessageLookupByLibrary.simpleMessage("Select Location"),
        "selectMultiple":
            MessageLookupByLibrary.simpleMessage("Select Multiple"),
        "selectNewStatus":
            MessageLookupByLibrary.simpleMessage("Select New Status"),
        "selectReservationDate":
            MessageLookupByLibrary.simpleMessage("Select Reservation Date"),
        "selectServices":
            MessageLookupByLibrary.simpleMessage("Select available services"),
        "selectedPeriodNotAvailable": MessageLookupByLibrary.simpleMessage(
            "The selected period is not available, please choose another period."),
        "sendTestNotification":
            MessageLookupByLibrary.simpleMessage("Send Test Notification"),
        "serverError": MessageLookupByLibrary.simpleMessage("Server Error"),
        "serviceFee": MessageLookupByLibrary.simpleMessage("Service fee"),
        "setPrices": MessageLookupByLibrary.simpleMessage("Set Prices"),
        "setPricesDescription": MessageLookupByLibrary.simpleMessage(
            "Set fixed price for all listings"),
        "settings": MessageLookupByLibrary.simpleMessage("Settings"),
        "share": MessageLookupByLibrary.simpleMessage("Share"),
        "shareCount": MessageLookupByLibrary.simpleMessage("Shares"),
        "shareProperty": MessageLookupByLibrary.simpleMessage("Share Property"),
        "shortTermBookings": MessageLookupByLibrary.simpleMessage(
            "Short-term bookings (≤28 days)"),
        "showAllAmenities":
            MessageLookupByLibrary.simpleMessage("Show all amenities"),
        "showAllReviews": MessageLookupByLibrary.simpleMessage("Show all"),
        "showComments": MessageLookupByLibrary.simpleMessage("Show Comments"),
        "showLess": MessageLookupByLibrary.simpleMessage("Show less"),
        "showMore": MessageLookupByLibrary.simpleMessage("Show more"),
        "since": MessageLookupByLibrary.simpleMessage("Since"),
        "skip": MessageLookupByLibrary.simpleMessage("Skip"),
        "smartEntry": MessageLookupByLibrary.simpleMessage("Smart Entry"),
        "smokingAllowed":
            MessageLookupByLibrary.simpleMessage("Smoking Allowed"),
        "sortBy": MessageLookupByLibrary.simpleMessage("Sort By"),
        "soundClick": MessageLookupByLibrary.simpleMessage("Click Sounds"),
        "soundClickDisabled":
            MessageLookupByLibrary.simpleMessage("Click sounds disabled"),
        "soundClickEnabled":
            MessageLookupByLibrary.simpleMessage("Click sounds enabled"),
        "soundScroll": MessageLookupByLibrary.simpleMessage("Scroll Sounds"),
        "soundScrollDisabled":
            MessageLookupByLibrary.simpleMessage("Scroll sounds disabled"),
        "soundScrollEnabled":
            MessageLookupByLibrary.simpleMessage("Scroll sounds enabled"),
        "soundSettings": MessageLookupByLibrary.simpleMessage("Sound Settings"),
        "spa": MessageLookupByLibrary.simpleMessage("Spa"),
        "start": MessageLookupByLibrary.simpleMessage("Start"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "statusActions": MessageLookupByLibrary.simpleMessage("Status Actions"),
        "statusChangeError":
            MessageLookupByLibrary.simpleMessage("Error changing status"),
        "statusChangedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Status changed successfully"),
        "streetView": MessageLookupByLibrary.simpleMessage("Street View"),
        "strictPolicy": MessageLookupByLibrary.simpleMessage("Strict"),
        "submit": MessageLookupByLibrary.simpleMessage("Submit"),
        "submitReview": MessageLookupByLibrary.simpleMessage("Submit Review"),
        "superhost": MessageLookupByLibrary.simpleMessage("Superhost"),
        "suspendedStatusDescription": MessageLookupByLibrary.simpleMessage(
            "Your listing has been suspended"),
        "tapToChangeImage":
            MessageLookupByLibrary.simpleMessage("Tap to change image"),
        "tapToUploadImages":
            MessageLookupByLibrary.simpleMessage("Tap to upload images"),
        "taxes": MessageLookupByLibrary.simpleMessage("Taxes"),
        "testNotification":
            MessageLookupByLibrary.simpleMessage("Test Notification"),
        "themeDisabled":
            MessageLookupByLibrary.simpleMessage("Light theme enabled"),
        "themeEnabled":
            MessageLookupByLibrary.simpleMessage("Dark theme enabled"),
        "thirdPartySharing":
            MessageLookupByLibrary.simpleMessage("Third Party Sharing"),
        "thirdPartySharingDesc": MessageLookupByLibrary.simpleMessage(
            "Information about data sharing with partners"),
        "thisMonth": MessageLookupByLibrary.simpleMessage("This Month"),
        "thisYear": MessageLookupByLibrary.simpleMessage("This Year"),
        "tip1": MessageLookupByLibrary.simpleMessage(
            "Add high-quality photos to attract more guests"),
        "tip2": MessageLookupByLibrary.simpleMessage(
            "Write a detailed description of your property"),
        "tip3": MessageLookupByLibrary.simpleMessage(
            "Set competitive pricing for your area"),
        "titleAndDescription":
            MessageLookupByLibrary.simpleMessage("Title & Description"),
        "topPerforming": MessageLookupByLibrary.simpleMessage("Top Performing"),
        "topRated": MessageLookupByLibrary.simpleMessage("Top Rated"),
        "total": MessageLookupByLibrary.simpleMessage("Total"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Total Amount"),
        "totalBookings": MessageLookupByLibrary.simpleMessage("Total Bookings"),
        "totalEarnings": MessageLookupByLibrary.simpleMessage("Total Earnings"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Total Price"),
        "totalProperties":
            MessageLookupByLibrary.simpleMessage("Total Properties"),
        "totalReservations":
            MessageLookupByLibrary.simpleMessage("Total Reservations"),
        "totalRevenue": MessageLookupByLibrary.simpleMessage("Total Revenue"),
        "totalReviews": MessageLookupByLibrary.simpleMessage("Total Reviews"),
        "totalValue": MessageLookupByLibrary.simpleMessage("Total Value"),
        "totalViews": MessageLookupByLibrary.simpleMessage("Total Views"),
        "totalWithdrawn":
            MessageLookupByLibrary.simpleMessage("Total Withdrawn"),
        "tourismPermitNumber":
            MessageLookupByLibrary.simpleMessage("Tourism Permit Number"),
        "transportation":
            MessageLookupByLibrary.simpleMessage("Transportation"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Try Again"),
        "tryDifferentKeywords": MessageLookupByLibrary.simpleMessage(
            "Try searching with different keywords"),
        "tryDifferentSearch": MessageLookupByLibrary.simpleMessage(
            "Try searching with different words"),
        "tryDifferentSearchCriteria": MessageLookupByLibrary.simpleMessage(
            "Try adjusting your search criteria"),
        "tv": MessageLookupByLibrary.simpleMessage("TV"),
        "underReview": MessageLookupByLibrary.simpleMessage("Under Review"),
        "uniqueViews": MessageLookupByLibrary.simpleMessage("Unique Views"),
        "unitDetails": MessageLookupByLibrary.simpleMessage("Unit Details"),
        "unitName": MessageLookupByLibrary.simpleMessage("Unit Name"),
        "unknownError": MessageLookupByLibrary.simpleMessage("Unknown Error"),
        "unknownStatusDescription":
            MessageLookupByLibrary.simpleMessage("Unknown status"),
        "unmuteVideo": MessageLookupByLibrary.simpleMessage("Unmute Video"),
        "verificationCodeSent": MessageLookupByLibrary.simpleMessage(
            "A 4-digit code has been sent to your phone"),
        "verificationFailed":
            MessageLookupByLibrary.simpleMessage("Verification failed"),
        "verified": MessageLookupByLibrary.simpleMessage("Verified"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Verify your phone number"),
        "version": MessageLookupByLibrary.simpleMessage("Version"),
        "veryGood": MessageLookupByLibrary.simpleMessage("Very Good"),
        "video": MessageLookupByLibrary.simpleMessage("Video"),
        "viewAll": MessageLookupByLibrary.simpleMessage("View All"),
        "viewAllBookings":
            MessageLookupByLibrary.simpleMessage("View All Bookings"),
        "viewAllReviews":
            MessageLookupByLibrary.simpleMessage("View All Reviews"),
        "viewDetails": MessageLookupByLibrary.simpleMessage("View Details"),
        "viewProfile": MessageLookupByLibrary.simpleMessage("View Profile"),
        "viewReservations":
            MessageLookupByLibrary.simpleMessage("View Reservations"),
        "views": MessageLookupByLibrary.simpleMessage("Views"),
        "viewsTrend": MessageLookupByLibrary.simpleMessage("Views Trend"),
        "walletBalance": MessageLookupByLibrary.simpleMessage("Wallet Balance"),
        "weekendDays": MessageLookupByLibrary.simpleMessage("Weekend Days"),
        "weekendPrice": MessageLookupByLibrary.simpleMessage("Weekend Price"),
        "weeklyPrice": MessageLookupByLibrary.simpleMessage("Weekly Price"),
        "welcomeGuest": MessageLookupByLibrary.simpleMessage(
            "Welcome our guest... continue"),
        "whatThisPlaceOffers":
            MessageLookupByLibrary.simpleMessage("What this place offers"),
        "whereYoullBe":
            MessageLookupByLibrary.simpleMessage("Where you\'ll be"),
        "wifi": MessageLookupByLibrary.simpleMessage("Wi-Fi"),
        "withdraw": MessageLookupByLibrary.simpleMessage("Withdraw"),
        "withdrawFunds": MessageLookupByLibrary.simpleMessage("Withdraw Funds"),
        "withdrawalMethod":
            MessageLookupByLibrary.simpleMessage("Withdrawal Method"),
        "workFriendly": MessageLookupByLibrary.simpleMessage("Work Friendly"),
        "workspace": MessageLookupByLibrary.simpleMessage("Workspace"),
        "writeComment":
            MessageLookupByLibrary.simpleMessage("Write a comment..."),
        "year": MessageLookupByLibrary.simpleMessage("year"),
        "years": MessageLookupByLibrary.simpleMessage("years"),
        "yearsHosting": MessageLookupByLibrary.simpleMessage("years hosting"),
        "yearsOnAirbnb":
            MessageLookupByLibrary.simpleMessage("Years on Airbnb"),
        "yourRights": MessageLookupByLibrary.simpleMessage("Your Rights"),
        "yourRightsDesc": MessageLookupByLibrary.simpleMessage(
            "Your privacy rights and how to exercise them")
      };
}
