import 'package:flutter/material.dart';
import 'package:gather_point/core/routing/routes_keys.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/home/<USER>/views/home_screen.dart';
import 'package:gather_point/feature/host/presentation/views/host_home_page.dart';
import 'package:gather_point/feature/profile/presentation/views/my_bookings_screen.dart';
import 'package:gather_point/feature/profile/presentation/views/profile_view.dart';
import 'package:gather_point/feature/reels/presentation/views/reels_page.dart';
import 'package:gather_point/feature/search/presentation/views/search_screen.dart';
import 'package:go_router/go_router.dart';
import 'package:hive/hive.dart';

// Global hoster mode notifier - single source of truth
final ValueNotifier<bool> isHosterModeNotifier = ValueNotifier(false);

// Initialize the notifier with current user's hoster mode status
void initializeHosterModeNotifier() {
  try {
    final userBox = Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
    final currentUser = userBox.get(AppConstants.kMyProfileKey);
    isHosterModeNotifier.value = currentUser?.isHosterMode ?? false;
    print('🔧 HosterModeNotifier initialized with value: ${isHosterModeNotifier.value}');
  } catch (e) {
    print('⚠️ Error initializing HosterModeNotifier: $e');
    isHosterModeNotifier.value = false;
  }
}

List<StatefulShellBranch> routesBranches() {
  return [
    StatefulShellBranch(
      routes: <RouteBase>[
        GoRoute(
          path: RoutesKeys.kHomeViewTab,
          builder: (context, state) => ValueListenableBuilder<bool>(
            valueListenable: isHosterModeNotifier,
            builder: (context, isHosterMode, child) {
              return isHosterMode
                  ? const HostHomePage()
                  : const GatherPointHome();
            },
          ),
        ),
      ],
    ),
    StatefulShellBranch(
      routes: <RouteBase>[
        GoRoute(
          path: RoutesKeys.kSearchViewTab,
          builder: (context, state) => ValueListenableBuilder<bool>(
            valueListenable: isHosterModeNotifier,
            builder: (context, isHosterMode, child) {
              return isHosterMode
                  ? const MyBookingsScreen()
                  : const SearchScreen();
            },
          ),
        ),
      ],
    ),
    StatefulShellBranch(
      routes: <RouteBase>[
        GoRoute(
          path: RoutesKeys.kFavoritesViewTab,
          builder: (context, state) => ValueListenableBuilder<bool>(
            valueListenable: isHosterModeNotifier,
            builder: (context, isHosterMode, child) {
              return isHosterMode
                  ? const MyListingScreen()
                  : const ReelsPage(
                      searchResults: [],
                      searchQuery: "",
                      serviceCategoryId: 1,
                    );
            },
          ),
        ),
      ],
    ),
    StatefulShellBranch(
      routes: <RouteBase>[
        GoRoute(
          path: RoutesKeys.kProfileViewTab,
          builder: (context, state) => const ProfileTabView(),
        ),
      ],
    ),
  ];
}

ValueListenableBuilder<bool> buildRoutesBranches() {
  return ValueListenableBuilder<bool>(
    valueListenable: isHosterModeNotifier,
    builder: (context, isHosterMode, child) {
      return Container(); // Replace with an appropriate widget or UI representation
    },
  );
}
