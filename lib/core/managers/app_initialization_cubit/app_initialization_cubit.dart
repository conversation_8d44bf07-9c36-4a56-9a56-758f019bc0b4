import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/routing/routes_branches.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/login_guest_use_case.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/validate_token_use_case.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:equatable/equatable.dart';

part 'app_initialization_state.dart';

class AppInitializationCubit extends Cubit<AppInitializationState> {
  final LoginGuestUseCase loginGuestUseCase;
  final ValidateTokenUseCase validateTokenUseCase;

  AppInitializationCubit({
    required this.loginGuestUseCase,
    required this.validateTokenUseCase,
  }) : super(AppInitializationInitial());

  /// Safe emit that checks if cubit is not closed
  void _safeEmit(AppInitializationState state) {
    if (!isClosed) {
      print('AppInitializationCubit: Emitting state: ${state.runtimeType}');
      emit(state);
    }
  }

  /// Initialize the app with proper flow
  Future<void> initializeApp() async {
    _safeEmit(AppInitializationLoading());

    try {
      // Check if user has viewed onboarding
      final settingsBox = Hive.box<bool>(AppConstants.kSettingsBoxName);
      final hasViewedOnboarding = settingsBox.get(AppConstants.kOnboardingCompletedKey) ?? false;

      if (!hasViewedOnboarding) {
        // First time user - show onboarding
        _safeEmit(AppInitializationShowOnboarding());
        return;
      }

      // User has viewed onboarding - check for existing token
      final profileBox = Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
      final existingUser = profileBox.get(AppConstants.kMyProfileKey);

      print('AppInitialization: Existing user found - isGuest: ${existingUser?.isGuest}, hasToken: ${existingUser?.token?.isNotEmpty}, isHosterMode: ${existingUser?.isHosterMode}');

      if (existingUser?.token != null && existingUser!.token.isNotEmpty) {
        // Validate existing token
        await _validateExistingToken(existingUser);
      } else {
        // No token - generate guest token and navigate to home
        print('AppInitialization: No valid token found, generating guest token');
        await _generateGuestTokenAndNavigate();
      }
    } catch (e) {
      // On any error, generate guest token and navigate to home
      await _generateGuestTokenAndNavigate();
    }
  }

  /// Complete onboarding and generate guest token
  Future<void> completeOnboarding() async {
    _safeEmit(AppInitializationLoading());

    try {
      // Mark onboarding as completed
      final settingsBox = Hive.box<bool>(AppConstants.kSettingsBoxName);
      await settingsBox.put(AppConstants.kOnboardingCompletedKey, true);

      // Generate guest token and navigate to home
      await _generateGuestTokenAndNavigate();
    } catch (e) {
      _safeEmit(AppInitializationError('Failed to complete onboarding: ${e.toString()}'));
    }
  }

  /// Validate existing token
  Future<void> _validateExistingToken(UserEntity user) async {
    try {
      print('AppInitialization: Validating token for user - isGuest: ${user.isGuest}, isHosterMode: ${user.isHosterMode}, id: ${user.id}');

      // Check if the existing user is a guest
      if (user.isGuest) {
        // For guest users, always generate a new guest token instead of validating the old one
        print('AppInitialization: User is guest, generating new guest token');
        await _generateGuestTokenAndNavigate();
        return;
      }

      // For non-guest users, validate their token
      print('AppInitialization: User is authenticated, validating token');
      final result = await validateTokenUseCase.call(user.token);

      result.fold(
        (failure) async {
          // Token is invalid - generate new guest token
          print('AppInitialization: Token validation failed: ${failure.errMessage}');
          await _generateGuestTokenAndNavigate();
        },
        (validUser) async {
          print('AppInitialization: Token validation succeeded - validUser hasToken: ${validUser.token.isNotEmpty}');
          print('AppInitialization: Original user - isGuest: ${user.isGuest}, isHosterMode: ${user.isHosterMode}');
          print('AppInitialization: ValidUser from API - isGuest: ${validUser.isGuest}, isHosterMode: ${validUser.isHosterMode}');
          // Token is valid - merge updated user data with existing token
          final profileBox = Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);

          // IMPORTANT: Preserve the original token and authentication state since the API doesn't return them
          final mergedUser = validUser.copyWith(
            token: user.token, // Preserve the original token
            id: validUser.id == 0 ? user.id : validUser.id, // Preserve ID if API doesn't return it
            // CRITICAL: Force isGuest to false for users who reach token validation
            // If we're validating a token, the user is authenticated, not a guest
            isGuest: false, // Force to false since we're validating an authenticated user's token
            otpApproved: user.otpApproved, // Preserve OTP status
            referral: validUser.referral.isEmpty ? user.referral : validUser.referral, // Preserve referral if not in response
          );

          print('AppInitialization: Merged user - preserving token: ${mergedUser.token.isNotEmpty ? "EXISTS" : "EMPTY"}');

          await profileBox.put(AppConstants.kMyProfileKey, mergedUser);

          // Update the global hoster mode notifier
          isHosterModeNotifier.value = mergedUser.isHosterMode;

          // Navigate to home with updated user data
          _safeEmit(AppInitializationNavigateToHome(user: mergedUser));
        },
      );
    } catch (e) {
      // On error, generate guest token
      await _generateGuestTokenAndNavigate();
    }
  }

  /// Generate guest token and navigate to home
  Future<void> _generateGuestTokenAndNavigate() async {
    try {
      final result = await loginGuestUseCase.call('');

      result.fold(
        (failure) {
          // If API fails, navigate to login screen as fallback
          _safeEmit(AppInitializationNavigateToLogin());
        },
        (guestUser) {
          // Save guest user to storage
          final profileBox = Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
          profileBox.put(AppConstants.kMyProfileKey, guestUser);

          // Update global hoster mode notifier
          isHosterModeNotifier.value = guestUser.isHosterMode;

          // Navigate to home
          _safeEmit(AppInitializationNavigateToHome(user: guestUser));
        },
      );
    } catch (e) {
      // If any exception occurs, navigate to login screen
      _safeEmit(AppInitializationNavigateToLogin());
    }
  }

  /// Force refresh token (for manual refresh scenarios)
  Future<void> refreshToken() async {
    _safeEmit(AppInitializationLoading());
    if (!isClosed) {
      await _generateGuestTokenAndNavigate();
    }
  }

  /// Reset app state (for logout scenarios)
  Future<void> resetAppState() async {
    try {
      // Clear user data
      final profileBox = Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
      await profileBox.clear();

      // Generate new guest token (only if cubit is still active)
      if (!isClosed) {
        await _generateGuestTokenAndNavigate();
      }
    } catch (e) {
      _safeEmit(AppInitializationError('Failed to reset app state: ${e.toString()}'));
    }
  }
}
