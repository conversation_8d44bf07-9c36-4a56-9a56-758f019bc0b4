import 'package:dio/dio.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/feature/auth/Data/Data%20Sources/login_local_data_source.dart';
import 'package:gather_point/feature/auth/Data/Data%20Sources/login_remote_data_source.dart';
import 'package:gather_point/feature/auth/Data/Repos/login_repo_impl.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/login_guest_use_case.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/login_token_use_case.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/login_use_case.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/resend_otp_use_case.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/validate_otp_use_case.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/validate_token_use_case.dart';
import 'package:gather_point/feature/host/data/services/host_api_service.dart';
import 'package:gather_point/feature/host/data/services/withdrawal_api_service.dart';
import 'package:gather_point/feature/host/data/services/my_listings_api_service.dart';
import 'package:gather_point/feature/host/data/services/mock_my_listings_api_service.dart';
import 'package:gather_point/feature/host/services/property_edit_service.dart';
import 'package:gather_point/feature/reservations/data/services/reservations_api_service.dart';
import 'package:gather_point/feature/dashboard/data/services/properties_api_service.dart';
import 'package:gather_point/feature/reviews/data/services/reviews_api_service.dart';
import 'package:gather_point/feature/profile/data/services/friends_api_service.dart';
import 'package:gather_point/feature/profile/data/services/co_host_api_service.dart';
import 'package:gather_point/feature/profile/data/services/early_access_api_service.dart';
import 'package:gather_point/feature/profile/data/services/support_api_service.dart';
import 'package:gather_point/feature/profile/data/services/legal_api_service.dart';
import 'package:gather_point/feature/profile/data/services/account_api_service.dart';
import 'package:gather_point/feature/profile/data/services/notification_api_service.dart';
import 'package:gather_point/core/services/location_service.dart';
import 'package:gather_point/core/services/shared_preferences_service.dart';
import 'package:gather_point/feature/profile/data/data_sources/profile_remote_data_source.dart';
import 'package:gather_point/feature/profile/data/repositories/profile_repository_impl.dart';
import 'package:gather_point/feature/profile/domain/repositories/profile_repository.dart';
import 'package:gather_point/feature/profile/domain/use_cases/get_user_info_use_case.dart';
import 'package:gather_point/feature/profile/domain/use_cases/edit_profile_use_case.dart';
import 'package:gather_point/feature/profile/domain/use_cases/logout_use_case.dart';
import 'package:gather_point/feature/profile/domain/use_cases/toggle_hoster_mode_use_case.dart';
import 'package:gather_point/feature/profile/domain/use_cases/delete_account_use_case.dart';
import 'package:gather_point/feature/profile/presentation/cubit/profile_cubit.dart';
import 'package:get_it/get_it.dart';
import 'package:hive/hive.dart';
import '../databases/api/dio_consumer.dart';

final getIt = GetIt.instance;

void setupServiceLocator() {
  //*** Shared Preferences Service ***/
  getIt.registerSingleton<SharedPreferencesService>(
    SharedPreferencesService(),
  );

  // Register Hive box first
  getIt.registerLazySingleton<Box<UserEntity>>(
    () => Hive.box<UserEntity>(AppConstants.kMyProfileBoxName),
  );
  getIt.registerSingleton<Box<bool>>(
      Hive.box<bool>(AppConstants.kSettingsBoxName));

  // Register Dio instance
  getIt.registerSingleton<Dio>(Dio());

  // Register DioConsumer with dependencies
  getIt.registerSingleton<DioConsumer>(
    DioConsumer(
      dio: getIt<Dio>(),
      profileBox: getIt<Box<UserEntity>>(), // Inject the Hive box
    ),
  );

  //*** FCM service ***/
  // FCM service doesn't need to be registered as it uses static methods

  //*** login service ***/
  getIt.registerSingleton<LoginRemoteDataSourceImpl>(
    LoginRemoteDataSourceImpl(
      apiConsumer: getIt<DioConsumer>(),
    ),
  );

  // Rest of your registrations remain the same...
  getIt.registerSingleton<LoginLocalDataSourceImpl>(LoginLocalDataSourceImpl());

  getIt.registerSingleton<LoginRepoImpl>(
    LoginRepoImpl(
      loginLocalDataSource: getIt<LoginLocalDataSourceImpl>(),
      loginRemoteDataSource: getIt<LoginRemoteDataSourceImpl>(),
    ),
  );

  //*** Login Use Cases ***/
  getIt.registerSingleton<LoginUseCase>(
    LoginUseCase(loginRepo: getIt<LoginRepoImpl>()),
  );

  getIt.registerSingleton<LoginGuestUseCase>(
    LoginGuestUseCase(loginRepo: getIt<LoginRepoImpl>()),
  );

  getIt.registerSingleton<ValidateOTPUseCase>(
    ValidateOTPUseCase(loginRepo: getIt<LoginRepoImpl>()),
  );

  getIt.registerSingleton<ResendOtpUseCase>(
    ResendOtpUseCase(loginRepo: getIt<LoginRepoImpl>()),
  );

  getIt.registerSingleton<LoginTokenUseCase>(
    LoginTokenUseCase(loginRepo: getIt<LoginRepoImpl>()),
  );

  getIt.registerSingleton<ValidateTokenUseCase>(
    ValidateTokenUseCase(loginRepo: getIt<LoginRepoImpl>()),
  );

  //*** API Services ***/
  getIt.registerSingleton<HostApiService>(
    HostApiService(getIt<DioConsumer>()),
  );

  getIt.registerSingleton<WithdrawalApiService>(
    WithdrawalApiService(getIt<DioConsumer>()),
  );

  getIt.registerSingleton<ReservationsApiService>(
    ReservationsApiService(getIt<DioConsumer>()),
  );

  getIt.registerSingleton<PropertiesApiService>(
    PropertiesApiService(getIt<DioConsumer>()),
  );

  getIt.registerSingleton<ReviewsApiService>(
    ReviewsApiService(getIt<DioConsumer>()),
  );

  getIt.registerSingleton<FriendsApiService>(
    FriendsApiService(getIt<DioConsumer>()),
  );

  getIt.registerSingleton<CoHostApiService>(
    CoHostApiService(getIt<DioConsumer>()),
  );

  getIt.registerSingleton<EarlyAccessApiService>(
    EarlyAccessApiService(getIt<DioConsumer>()),
  );

  getIt.registerSingleton<SupportApiService>(
    SupportApiService(getIt<DioConsumer>()),
  );

  getIt.registerSingleton<LegalApiService>(
    LegalApiService(getIt<DioConsumer>()),
  );

  getIt.registerSingleton<AccountApiService>(
    AccountApiService(getIt<DioConsumer>()),
  );

  getIt.registerSingleton<NotificationApiService>(
    NotificationApiService(getIt<DioConsumer>()),
  );

  // Host Management Services
  getIt.registerSingleton<MyListingsApiService>(
    // Use mock service for development when backend is not available
    const bool.fromEnvironment('USE_MOCK_API', defaultValue: false)
        ? MockMyListingsApiService()
        : MyListingsApiService(getIt<DioConsumer>()),
  );

  getIt.registerSingleton<PropertyEditService>(
    PropertyEditService(getIt<DioConsumer>()),
  );

  //*** Location Service ***/
  getIt.registerSingleton<LocationService>(
    LocationService(),
  );

  //*** Profile Services ***/
  getIt.registerSingleton<ProfileRemoteDataSourceImpl>(
    ProfileRemoteDataSourceImpl(
      apiConsumer: getIt<DioConsumer>(),
    ),
  );

  getIt.registerSingleton<ProfileRepository>(
    ProfileRepositoryImpl(
      remoteDataSource: getIt<ProfileRemoteDataSourceImpl>(),
    ),
  );

  //*** Profile Use Cases ***/
  getIt.registerSingleton<GetUserInfoUseCase>(
    GetUserInfoUseCase(getIt<ProfileRepository>()),
  );

  getIt.registerSingleton<EditProfileUseCase>(
    EditProfileUseCase(getIt<ProfileRepository>()),
  );

  getIt.registerSingleton<LogoutUseCase>(
    LogoutUseCase(getIt<ProfileRepository>()),
  );

  getIt.registerSingleton<ToggleHosterModeUseCase>(
    ToggleHosterModeUseCase(getIt<ProfileRepository>()),
  );

  getIt.registerSingleton<DeleteAccountUseCase>(
    DeleteAccountUseCase(getIt<ProfileRepository>()),
  );

  //*** Profile Cubit ***/
  getIt.registerFactory<ProfileCubit>(
    () => ProfileCubit(
      getUserInfoUseCase: getIt<GetUserInfoUseCase>(),
      editProfileUseCase: getIt<EditProfileUseCase>(),
      logoutUseCase: getIt<LogoutUseCase>(),
      toggleHosterModeUseCase: getIt<ToggleHosterModeUseCase>(),
      deleteAccountUseCase: getIt<DeleteAccountUseCase>(),
    ),
  );
}
