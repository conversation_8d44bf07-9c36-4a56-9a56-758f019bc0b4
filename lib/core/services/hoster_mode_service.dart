import 'package:dio/dio.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/routing/routes_branches.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/utils/auth_utils.dart';

/// Service for handling hoster mode operations
class HosterModeService {
  static final DioConsumer _dioConsumer = getIt<DioConsumer>();

  /// Toggle hoster mode for the current user
  static Future<bool> toggleHosterMode(bool isHosterMode) async {
    try {
      final response = await _dioConsumer.post(
        '/api/client/toggle_hoster_mode',
        data: {
          'is_hoster_mode': isHosterMode,
        },
      );

      if (response['code'] == 200 && response['status'] == true && response['data'] != null) {
        // Update local user data with the response from server
        final userData = response['data']['user'];
        if (userData != null) {
          final currentUser = AuthUtils.getCurrentUser();
          if (currentUser != null) {
            print('HosterModeService: Current user token: ${currentUser.token.isNotEmpty ? "EXISTS" : "EMPTY"}');
            print('HosterModeService: Current user isGuest: ${currentUser.isGuest}');
            print('HosterModeService: Toggling TO hoster mode: $isHosterMode');
            print('HosterModeService: API returned token: ${userData['token']}');
            print('HosterModeService: API returned is_guest: ${userData['is_guest']}');
            print('HosterModeService: API returned is_hoster_mode: ${userData['is_hoster_mode']}');

            final updatedUser = currentUser.copyWith(
              isHosterMode: userData['is_hoster_mode'] ?? isHosterMode,
              fullName: userData['full_name'],
              phone: userData['phone']?.toString(),
              email: userData['email'],
              bio: userData['bio']?.toString(),
              image: userData['profile_picture'],
              gender: userData['gender'],
              userType: userData['user_type_id'],
              // IMPORTANT: Preserve the existing token since the API doesn't return it
              token: currentUser.token,
              // Also preserve other important fields that might not be in the response
              id: currentUser.id,
              otpApproved: currentUser.otpApproved,
              // CRITICAL: Always preserve isGuest as false for authenticated users
              // The API might incorrectly return is_guest: true for client mode users
              isGuest: false, // Force to false since this is an authenticated user making API calls
              referral: currentUser.referral,
              username: userData['username'] ?? currentUser.username,
              birthdate: userData['birthdate'] ?? currentUser.birthdate,
            );

            print('HosterModeService: Updated user token: ${updatedUser.token.isNotEmpty ? "EXISTS" : "EMPTY"}');
            print('HosterModeService: Updated user isGuest: ${updatedUser.isGuest}');
            print('HosterModeService: Updated user isHosterMode: ${updatedUser.isHosterMode}');

            // Save updated user to local storage
            await AuthUtils.updateUser(updatedUser);

            // Small delay to ensure data is properly persisted
            await Future.delayed(const Duration(milliseconds: 50));

            // Update the global hoster mode notifier
            isHosterModeNotifier.value = updatedUser.isHosterMode;

            return true;
          }
        }
      }
      
      return false;
    } on DioException catch (e) {
      // Handle API errors
      // TODO: Replace with proper logging framework
      // print('HosterModeService Error: ${e.message}');
      // if (e.response?.data != null) {
      //   print('Error Response: ${e.response?.data}');
      // }
      return false;
    } catch (e) {
      // TODO: Replace with proper logging framework
      // print('HosterModeService Unexpected Error: $e');
      return false;
    }
  }

  /// Get current hoster mode status
  static bool getCurrentHosterMode() {
    return AuthUtils.isInHosterMode();
  }

  /// Check if user can toggle hoster mode (must be authenticated, not guest)
  static bool canToggleHosterMode() {
    return AuthUtils.isAuthenticated() && !AuthUtils.isGuest();
  }
}
